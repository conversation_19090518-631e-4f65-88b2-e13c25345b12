// Add helper function for editing layer properties
window.editLayerProperty = function(property) {
    let layer = player.mod.selectedLayer
    if (!layer) return
    
    switch(property) {
        case "name":
            let name = prompt(`Enter new ${property} value for layer ${layer}:`)
            if (name === null) return
            layers[layer].name = name
            tmp[layer].name = name
            break
        case "color":
            // Create color picker overlay
            let overlay = document.createElement('div')
            overlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.8);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
            `
            
            let colorPicker = document.createElement('div')
            colorPicker.style.cssText = `
                background: #222;
                padding: 20px;
                border-radius: 10px;
                min-width: 300px;
            `
            
            colorPicker.innerHTML = `
                <h3 style='margin-bottom: 15px;'>Select Layer Color</h3>
                <input type='color' id='colorWheel' value='${layers[layer].color}' style='width: 100%; height: 50px; margin-bottom: 10px;'>
                <input type='text' id='colorHex' value='${layers[layer].color}' style='width: 100%; padding: 8px; margin-bottom: 15px; background: #333; border: 1px solid #444; color: white; border-radius: 5px;'>
                <div style='display: flex; gap: 10px;'>
                    <button id='saveColor' style='flex: 1; padding: 10px; background: #4BDC13; border: none; border-radius: 5px; color: white; cursor: pointer;'>Save</button>
                    <button id='cancelColor' style='flex: 1; padding: 10px; background: #666; border: none; border-radius: 5px; color: white; cursor: pointer;'>Cancel</button>
                </div>
            `
            
            overlay.appendChild(colorPicker)
            document.body.appendChild(overlay)
            
            // Setup event listeners
            document.getElementById('colorWheel').addEventListener('input', (e) => {
                document.getElementById('colorHex').value = e.target.value
            })
            
            document.getElementById('colorHex').addEventListener('input', (e) => {
                if (/^#[0-9A-F]{6}$/i.test(e.target.value)) {
                    document.getElementById('colorWheel').value = e.target.value
                }
            })
            
            document.getElementById('saveColor').addEventListener('click', () => {
                let color = document.getElementById('colorHex').value
                if (/^#[0-9A-F]{6}$/i.test(color)) {
                    layers[layer].color = color
                    tmp[layer].color = color
                    needCanvasUpdate = true
                    saveLayerConfig(layer)
                }
                document.body.removeChild(overlay)
            })
            
            document.getElementById('cancelColor').addEventListener('click', () => {
                document.body.removeChild(overlay)
            })
            break
        case "symbol":
            // Create symbol editor overlay
            let symbolOverlay = document.createElement('div')
            symbolOverlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.8);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
            `
            
            let symbolEditor = document.createElement('div')
            symbolEditor.style.cssText = `
                background: #222;
                padding: 20px;
                border-radius: 10px;
                min-width: 400px;
                max-width: 90%;
            `
            
            symbolEditor.innerHTML = `
                <h3 style='margin-bottom: 15px;'>Edit Layer Symbol</h3>
                <div style='margin-bottom: 20px;'>
                    <label style='display: block; margin-bottom: 10px;'>Symbol Type</label>
                    <select id='symbolType' style='width: 100%; padding: 8px; background: #333; border: 1px solid #444; color: white; border-radius: 5px;'>
                        <option value='text' ${!layers[layer].symbolIsImage ? 'selected' : ''}>Text</option>
                        <option value='image' ${layers[layer].symbolIsImage ? 'selected' : ''}>Image</option>
                    </select>
                </div>
                
                <div id='textOptions' style='display: ${!layers[layer].symbolIsImage ? 'block' : 'none'}; margin-bottom: 20px;'>
                    <label style='display: block; margin-bottom: 10px;'>Text Symbol</label>
                    <input type='text' id='symbolText' value='${layers[layer].symbol}' style='width: 100%; padding: 8px; background: #333; border: 1px solid #444; color: white; border-radius: 5px; margin-bottom: 10px;'>
                    
                    <label style='display: block; margin-bottom: 10px;'>Font Family</label>
                    <select id='symbolFont' style='width: 100%; padding: 8px; background: #333; border: 1px solid #444; color: white; border-radius: 5px;'>
                        <option value='Orbitron' ${layers[layer].symbolFont === 'Orbitron' ? 'selected' : ''}>Orbitron</option>
                        <option value='Inconsolata' ${layers[layer].symbolFont === 'Inconsolata' ? 'selected' : ''}>Inconsolata</option>
                        <option value='Arial' ${layers[layer].symbolFont === 'Arial' ? 'selected' : ''}>Arial</option>
                    </select>
                </div>
                
                <div id='imageOptions' style='display: ${layers[layer].symbolIsImage ? 'block' : 'none'}; margin-bottom: 20px;'>
                    <label style='display: block; margin-bottom: 10px;'>Image URL</label>
                    <input type='text' id='symbolImage' value='${layers[layer].symbolImage || ''}' style='width: 100%; padding: 8px; background: #333; border: 1px solid #444; color: white; border-radius: 5px; margin-bottom: 10px;'>
                    
                    <label style='display: block; margin-bottom: 10px;'>Rendering Quality</label>
                    <select id='imageQuality' style='width: 100%; padding: 8px; background: #333; border: 1px solid #444; color: white; border-radius: 5px;'>
                        <option value='default' ${!layers[layer].symbolPixelPerfect ? 'selected' : ''}>Default</option>
                        <option value='pixelPerfect' ${layers[layer].symbolPixelPerfect ? 'selected' : ''}>Pixel Perfect (Max Quality)</option>
                    </select>
                </div>
                
                <div style='display: flex; gap: 10px;'>
                    <button id='saveSymbol' style='flex: 1; padding: 10px; background: #4BDC13; border: none; border-radius: 5px; color: white; cursor: pointer;'>Save</button>
                    <button id='cancelSymbol' style='flex: 1; padding: 10px; background: #666; border: none; border-radius: 5px; color: white; cursor: pointer;'>Cancel</button>
                </div>
            `
            
            symbolOverlay.appendChild(symbolEditor)
            document.body.appendChild(symbolOverlay)
            
            // Setup event listeners
            document.getElementById('symbolType').addEventListener('change', (e) => {
                document.getElementById('textOptions').style.display = e.target.value === 'text' ? 'block' : 'none'
                document.getElementById('imageOptions').style.display = e.target.value === 'image' ? 'block' : 'none'
            })
            
            document.getElementById('saveSymbol').addEventListener('click', () => {
                let isImage = document.getElementById('symbolType').value === 'image'
                if (isImage) {
                    layers[layer].symbolIsImage = true
                    layers[layer].symbolImage = document.getElementById('symbolImage').value
                    layers[layer].symbolPixelPerfect = document.getElementById('imageQuality').value === 'pixelPerfect'
                    layers[layer].symbol = ''  // Clear text symbol
                    
                    // Ensure layer functions return Decimals
                    let originalBaseAmount = layers[layer].baseAmount
                    layers[layer].baseAmount = function() {
                        let amount = originalBaseAmount()
                        return amount instanceof Decimal ? amount : new Decimal(amount)
                    }
                } else {
                    layers[layer].symbolIsImage = false
                    layers[layer].symbol = document.getElementById('symbolText').value
                    layers[layer].symbolFont = document.getElementById('symbolFont').value
                    delete layers[layer].symbolImage
                    delete layers[layer].symbolPixelPerfect
                }
                
                // Update temporary data
                Object.assign(tmp[layer], layers[layer])
                if (tmp[layer]) {
                    tmp[layer].baseAmount = layers[layer].baseAmount()
                }
                needCanvasUpdate = true
                saveLayerConfig(layer)
                document.body.removeChild(symbolOverlay)
            })
            
            document.getElementById('cancelSymbol').addEventListener('click', () => {
                document.body.removeChild(symbolOverlay)
            })
            break
    }
    
    // Save layer configuration
    saveLayerConfig(layer)
    
    // Force update
    updateTemp()
    updateTabFormats()
}

window.editLayerMechanic = function(mechanic) {
    let layer = player.mod.selectedLayer
    if (!layer) return
    
    switch(mechanic) {
        case "type":
            createCustomPrompt("Enter layer type (normal or static):", layers[layer].type || "normal", (type) => {
            if (type === null) return
            if (type !== "normal" && type !== "static") {
                alert("Invalid type. Must be 'normal' or 'static'")
                return
            }
            layers[layer].type = type
            tmp[layer].type = type
                saveLayerConfig(layer)
                updateTemp()
                updateTabFormats()
            })
            break
        case "resource":
            createCustomPrompt("Enter resource name:", layers[layer].resource, (resource) => {
            if (resource === null) return
            layers[layer].resource = resource
            tmp[layer].resource = resource
                saveLayerConfig(layer)
                updateTemp()
                updateTabFormats()
            })
            break
        case "requires":
            createCustomPrompt("Enter required amount:", layers[layer].requires, (requires) => {
            if (requires === null) return
            try {
                layers[layer].requires = new Decimal(requires)
                tmp[layer].requires = new Decimal(requires)
                    saveLayerConfig(layer)
                    updateTemp()
                    updateTabFormats()
            } catch(e) {
                alert("Invalid number")
                return
            }
            })
            break
        case "resetDescription":
            createCustomPrompt("Enter prestige button text:", layers[layer].resetDescription || "Reset for ", (resetDesc) => {
            if (resetDesc === null) return
            layers[layer].resetDescription = resetDesc
            tmp[layer].resetDescription = resetDesc
            // Initialize prestige button text if not set
            if (!layers[layer].prestigeButtonText) {
                layers[layer].prestigeButtonText = function() {
                    return (tmp[layer].resetDescription || "Reset for ") + " " + formatWhole(tmp[layer].resetGain) + " " + tmp[layer].resource
                }
            }
                saveLayerConfig(layer)
                updateTemp()
                updateTabFormats()
            })
            break
    }
    
    // The saveLayerConfig, updateTemp, and updateTabFormats calls are now handled inside the callback of createCustomPrompt
    // This is because createCustomPrompt is asynchronous, and these actions should only happen after the user provides input.
} 