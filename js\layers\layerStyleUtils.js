function getNodeStyle(layer) {
    let style = layers[layer].nodeStyle || {}
    if (layers[layer].symbolIsImage) {
        style = {
            ...style,
            "background-image": `url(${layers[layer].symbolImage})`,
            "background-size": layers[layer].symbolPixelPerfect ? "100% 100%" : "contain",
            "background-position": "center",
            "background-repeat": "no-repeat",
            "font-size": "0px"  // Hide any text
        }
        
        // Ensure all layer functions return Decimals
        if (layers[layer].gainExp) {
            let originalGainExp = layers[layer].gainExp
            layers[layer].gainExp = function() { return ensureDecimal(originalGainExp) }
        }
        if (layers[layer].gainMult) {
            let originalGainMult = layers[layer].gainMult
            layers[layer].gainMult = function() { return ensureDecimal(originalGainMult) }
        }
        if (layers[layer].baseAmount) {
            let originalBaseAmount = layers[layer].baseAmount
            layers[layer].baseAmount = function() { return ensureDecimal(originalBaseAmount) }
        }
        
        // Update tmp values
        if (tmp[layer]) {
            tmp[layer].gainExp = ensureDecimal(layers[layer].gainExp)
            tmp[layer].gainMult = ensureDecimal(layers[layer].gainMult)
            tmp[layer].baseAmount = ensureDecimal(layers[layer].baseAmount)
            tmp[layer].requires = ensureDecimal(layers[layer].requires)
            tmp[layer].exponent = ensureDecimal(layers[layer].exponent)
        }
    } else if (layers[layer].symbolFont) {
        style = {
            ...style,
            "font-family": layers[layer].symbolFont
        }
    }
    return style
} 