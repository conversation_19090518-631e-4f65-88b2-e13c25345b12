// js/features/editorUtils.js

// Helper function to generate a text input
window.generateTextInput = function(id, label, value = '', style = '') {
    return `
        <div>
            <label for="${id}">${label}:</label>
            <input type="text" id="${id}" value="${value}" style="${style} width: 100%; margin-top: 5px;">
        </div>
    `;
}

// Helper for number input
window.generateNumberInput = function(id, label, value = 0, style = '') {
    return `
        <div>
            <label for="${id}">${label}:</label>
            <input type="number" id="${id}" value="${value}" style="${style} width: 100%; margin-top: 5px;">
        </div>
    `;
}

// Helper for textarea
window.generateTextarea = function(id, label, value = '', rows = 3, style = '') {
    return `
        <div>
            <label for="${id}">${label}:</label>
            <textarea id="${id}" rows="${rows}" style="${style} width: 100%; margin-top: 5px;">${value}</textarea>
        </div>
    `;
}

// Helper for checkbox
window.generateCheckbox = function(id, label, checked = false) {
    return `
        <div>
            <input type="checkbox" id="${id}" ${checked ? 'checked' : ''}>
            <label for="${id}">${label}</label>
        </div>
    `;
}

// Helper for color picker
window.generateColorPicker = function(id, label, value = '#000000') {
    return `
        <div>
            <label for="${id}">${label}:</label>
            <input type="color" id="${id}" value="${value}" style="width: 100%; margin-top: 5px;">
        </div>
    `;
}

// Helper for select dropdown
window.generateSelect = function(id, label, options, selected) {
    let optionHtml = options.map(opt => `<option value="${opt.value}" ${opt.value === selected ? 'selected' : ''}>${opt.label}</option>`).join('');
    return `
        <div>
            <label for="${id}">${label}:</label>
            <select id="${id}" style="width: 100%; margin-top: 5px;">
                ${optionHtml}
            </select>
        </div>
    `;
}

// More helpers can be added as needed 