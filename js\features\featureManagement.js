function addFeature(layerId, featureType) {
    let layer = layers[layerId]
    if (!layer) return

    let nextId = Object.keys(layer[featureType] || {}).length + 1
    let id = nextId.toString()

    // Initialize the feature object if it doesn't exist
    if (!layer[featureType]) layer[featureType] = {}

    // Get default values
    const defaults = {
        upgrades: {
            title: 'New Upgrade',
            description: 'This is a new upgrade',
            cost: new Decimal(10),
            effect() { return new Decimal(1) },
            effectDisplay() { return "" },
            unlocked() { return true }
        },
        milestones: {
            requirementDescription: 'Reach X points',
            effectDescription: 'This is a new milestone',
            done() { return false },
            unlocked() { return true }
        },
        challenges: {
            name: 'New Challenge',
            description: 'This is a new challenge',
            goal: new Decimal(10),
            rewardDescription: 'Reward description',
            rewardEffect() { return new Decimal(1) },
            rewardDisplay() { return "" },
            unlocked() { return true },
            completionLimit: 1
        }
    }[featureType]

    layer[featureType][id] = { ...defaults }

    // Update temporary data
    if (!tmp[layerId]) tmp[layerId] = {}
    if (!tmp[layerId][featureType]) tmp[layerId][featureType] = {}
    tmp[layerId][featureType][id] = layer[featureType][id]

    // Save layer configuration
    saveLayerConfig(layerId)

    // Force update
    if (!window.isUpdating) {
        window.isUpdating = true
        updateTemp()
        updateTabFormats()
        window.isUpdating = false
    }

    // Refresh feature editor and open edit dialog for new feature
    // The overlay management is now centralized in openFeatureEditor
    openFeatureEditor(layerId, featureType, id);
}

function deleteFeature(layerId, featureType, featureId) {
    if (!confirm(`Are you sure you want to delete this ${featureType.slice(0, -1)}?`)) return

    let layer = layers[layerId]
    delete layer[featureType][featureId]

    // Update temporary data
    if (tmp[layerId] && tmp[layerId][featureType]) {
        delete tmp[layerId][featureType][featureId]
    }

    // Save layer configuration
    saveLayerConfig(layerId)

    // Force update
    if (!window.isUpdating) {
        window.isUpdating = true
        updateTemp()
        updateTabFormats()
        window.isUpdating = false
    }

    // Refresh feature editor
    // The overlay management is now centralized in openFeatureEditor
    openFeatureEditor(layerId, featureType);
}

function openFeatureEditor(layerId, featureType, featureIdToEdit = null) {
    layerId = String(layerId);
    let layer = layers[layerId];
    if (!layer) {
        console.error(`openFeatureEditor: Layer '${layerId}' is undefined.`);
        return;
    }

    let overlay = document.querySelector('.feature-editor-overlay');
    let editor = document.querySelector('.feature-editor-dialog');

    if (!overlay) {
        overlay = document.createElement('div');
        overlay.className = 'feature-editor-overlay';
        document.body.insertAdjacentElement('beforeend', overlay);
    }

    if (!editor) {
        editor = document.createElement('div');
        editor.className = 'feature-editor-dialog';
        overlay.appendChild(editor);
    }

    const featureInfo = {
        upgrades: {
            icon: 'fa-star',
            title: 'Upgrades',
            getHTML: getUpgradeEditorHTML // Reference to the HTML generation function
        },
        milestones: {
            icon: 'fa-trophy',
            title: 'Milestones',
            getHTML: getMilestoneEditorHTML
        },
        challenges: {
            icon: 'fa-sword',
            title: 'Challenges',
            getHTML: getChallengeEditorHTML
            }
    };

    const info = featureInfo[featureType];
    let contentHTML = '';
    let titleText = '';

    if (featureIdToEdit) {
        // Display individual feature editor
        contentHTML = info.getHTML(layerId, featureIdToEdit);
        titleText = `Edit ${featureType.charAt(0).toUpperCase() + featureType.slice(1).slice(0, -1)}`;
    } else {
        // Display list of features
        titleText = `${info.title} Editor`;
        contentHTML = `
        <div class='feature-list'>
            ${(function() {
                switch(featureType) {
                    case 'upgrades': return getUpgradesHTML(layerId, layer);
                    case 'milestones': return getMilestonesHTML(layerId, layer);
                    case 'challenges': return getChallengesHTML(layerId, layer);
                    default: return '';
                }
            })()}
        </div>

        <div style="margin-top: 20px; text-align: center;">
                <button onclick="addFeature('${layerId}', '${featureType}'); openFeatureEditor('${layerId}', '${featureType}', null);" style="
                background: #4CAF50;
                border: none;
                padding: 12px 25px;
                border-radius: 8px;
                color: white;
                font-size: 16px;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 10px;
                margin: 0 auto;
            ">
                <i class="fas fa-plus"></i> Add New ${info.title.slice(0, -1)}
            </button>
        </div>
        `;

        // If there are no features of this type, automatically add one and open its editor
        if (Object.keys(layer[featureType] || {}).length === 0) {
            addFeature(layerId, featureType);  // Add the feature but do not open it automatically
            // Do not call openFeatureEditor recursively here
        }
    }

    editor.innerHTML = `
        <div class="editor-header">
            <h2><i class="fas ${info.icon}"></i> ${titleText}</h2>
            <button class="close-button" onclick='closeFeatureEditor()'><i class="fas fa-times"></i></button>
        </div>
        ${contentHTML}
    `;

    // Attach event listeners for dynamically loaded content
    if (featureIdToEdit) {
    if (featureType === 'upgrades') {
            const effectTypeSelect = document.getElementById('effect-type');
            if (effectTypeSelect) {
                effectTypeSelect.addEventListener('change', function(e) {
                    const customEffectContainer = document.getElementById('custom-effect-container');
                    if (customEffectContainer) {
                        customEffectContainer.style.display = e.target.value === 'custom' ? 'block' : 'none';
                    }
                });
            }

            const unlockTypeSelect = document.getElementById('unlock-type');
            if (unlockTypeSelect) {
                unlockTypeSelect.addEventListener('change', function(e) {
                    const unlockValueContainer = document.getElementById('unlock-value-container');
                    const customUnlockContainer = document.getElementById('custom-unlock-container');
                    if (unlockValueContainer) {
                        unlockValueContainer.style.display = e.target.value !== 'always' && e.target.value !== 'custom' ? 'block' : 'none';
                    }
                    if (customUnlockContainer) {
                        customUnlockContainer.style.display = e.target.value === 'custom' ? 'block' : 'none';
                    }
                });
            }

            // Dynamic for title-is-function
            const titleCheckbox = document.getElementById('title-is-function');
            if (titleCheckbox) {
                titleCheckbox.addEventListener('change', function() {
                    const content = document.getElementById('title-content');
                    const currentValue = document.getElementById('upgrade-title') ? document.getElementById('upgrade-title').value : '';
                    content.innerHTML = this.checked ? 
                        `<textarea id="upgrade-title" placeholder="Enter title function code">${currentValue}</textarea>` :
                        `<input type="text" id="upgrade-title" value="${currentValue}" placeholder="Enter title">`;
                });
            }
            
            // Dynamic for description-is-function (both textarea, update placeholder)
            const descCheckbox = document.getElementById('description-is-function');
            if (descCheckbox) {
                descCheckbox.addEventListener('change', function() {
                    const content = document.getElementById('description-content');
                    const currentValue = document.getElementById('upgrade-description') ? document.getElementById('upgrade-description').value : '';
                    content.innerHTML = `<textarea id="upgrade-description" placeholder="${this.checked ? 'Enter description function code' : 'Enter description'}">${currentValue}</textarea>`;
                });
            }
        } else if (featureType === 'milestones') {
            const doneTypeSelect = document.getElementById('done-type');
            if (doneTypeSelect) {
                doneTypeSelect.addEventListener('change', function(e) {
                    const doneValueContainer = document.getElementById('done-value-container');
                    const customDoneContainer = document.getElementById('custom-done-container');
                    if (doneValueContainer) {
                        doneValueContainer.style.display = e.target.value !== 'always' && e.target.value !== 'custom' ? 'block' : 'none';
                    }
                    if (customDoneContainer) {
                        customDoneContainer.style.display = e.target.value === 'custom' ? 'block' : 'none';
                    }
                });
            }

            // Dynamic for req-desc-is-function
            const reqCheckbox = document.getElementById('req-desc-is-function');
            if (reqCheckbox) {
                reqCheckbox.addEventListener('change', function() {
                    const content = document.getElementById('req-desc-content');
                    const currentValue = document.getElementById('milestone-requirement') ? document.getElementById('milestone-requirement').value : '';
                    content.innerHTML = this.checked ? 
                        `<textarea id="milestone-requirement" placeholder="Enter requirement function code">${currentValue}</textarea>` :
                        `<input type="text" id="milestone-requirement" value="${currentValue}" placeholder="Enter requirement description">`;
                });
            }
            
            // Dynamic for effect-desc-is-function
            const effectCheckbox = document.getElementById('effect-desc-is-function');
            if (effectCheckbox) {
                effectCheckbox.addEventListener('change', function() {
                    const content = document.getElementById('effect-desc-content');
                    const currentValue = document.getElementById('milestone-effect-description') ? document.getElementById('milestone-effect-description').value : '';
                    content.innerHTML = `<textarea id="milestone-effect-description" placeholder="${this.checked ? 'Enter effect function code' : 'Enter effect description'}">${currentValue}</textarea>`;
                });
            }
        } else if (featureType === 'challenges') {
            const rewardEffectTypeSelect = document.getElementById('reward-effect-type');
            if (rewardEffectTypeSelect) {
                rewardEffectTypeSelect.addEventListener('change', function(e) {
                    const customRewardEffectContainer = document.getElementById('custom-reward-effect-container');
                    if (customRewardEffectContainer) {
                        customRewardEffectContainer.style.display = e.target.value === 'custom' ? 'block' : 'none';
                    }
                });
            }

            const unlockTypeSelect = document.getElementById('unlock-type');
            if (unlockTypeSelect) {
                unlockTypeSelect.addEventListener('change', function(e) {
                    const unlockValueContainer = document.getElementById('unlock-value-container');
                    const customUnlockContainer = document.getElementById('custom-unlock-container');
                    if (unlockValueContainer) {
                        unlockValueContainer.style.display = e.target.value !== 'always' && e.target.value !== 'custom' ? 'block' : 'none';
                    }
                    if (customUnlockContainer) {
                        customUnlockContainer.style.display = e.target.value === 'custom' ? 'block' : 'none';
                    }
                });
            }

            // Dynamic for goal-is-function
            const goalCheckbox = document.getElementById('goal-is-function');
            if (goalCheckbox) {
                goalCheckbox.addEventListener('change', function() {
                    const content = document.getElementById('goal-content');
                    const currentValue = document.getElementById('challenge-goal') ? document.getElementById('challenge-goal').value : '';
                    content.innerHTML = this.checked ? 
                        `<textarea id="challenge-goal" placeholder="Enter goal function code">${currentValue}</textarea>` :
                        `<input type="number" id="challenge-goal" value="${parseFloat(currentValue) || 0}" placeholder="Enter goal value">`;
                });
            }
            
            // Dynamic for reward-desc-is-function
            const rewardCheckbox = document.getElementById('reward-desc-is-function');
            if (rewardCheckbox) {
                rewardCheckbox.addEventListener('change', function() {
                    const content = document.getElementById('reward-desc-content');
                    const currentValue = document.getElementById('challenge-reward-description') ? document.getElementById('challenge-reward-description').value : '';
                    content.innerHTML = `<textarea id="challenge-reward-description" placeholder="${this.checked ? 'Enter reward description function code' : 'Enter reward description'}">${currentValue}</textarea>`;
                });
            }
        }
    }
}

function editFeature(layerId, featureType, featureId) {
    openFeatureEditor(layerId, featureType, featureId);
}

function closeFeatureEditor() {
    let overlay = document.querySelector('.feature-editor-overlay');
    if (overlay) overlay.remove();
} 