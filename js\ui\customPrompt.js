
function createCustomPrompt(message, defaultValue, callback) {
    let overlay = document.createElement('div')
    overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.8);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 99999;
    `
    
    let promptBox = document.createElement('div')
    promptBox.style.cssText = `
        background: #222;
        padding: 20px;
        border-radius: 10px;
        min-width: 300px;
    `
    
    promptBox.innerHTML = `
        <h3 style='margin-bottom: 15px;'>${message}</h3>
        <input type='text' id='customPromptInput' value='${defaultValue}' style='width: 100%; padding: 8px; margin-bottom: 15px; background: #333; border: 1px solid #444; color: white; border-radius: 5px;'>
        <div style='display: flex; gap: 10px;'>
            <button id='customPromptOk' style='flex: 1; padding: 10px; background: #4BDC13; border: none; border-radius: 5px; color: white; cursor: pointer;'>OK</button>
            <button id='customPromptCancel' style='flex: 1; padding: 10px; background: #666; border: none; border-radius: 5px; color: white; cursor: pointer;'>Cancel</button>
        </div>
    `
    
    overlay.appendChild(promptBox)
    document.body.appendChild(overlay)
    
    const inputElement = document.getElementById('customPromptInput')
    inputElement.focus()
    inputElement.select()

    document.getElementById('customPromptOk').addEventListener('click', () => {
        callback(inputElement.value)
        document.body.removeChild(overlay)
    })
    
    document.getElementById('customPromptCancel').addEventListener('click', () => {
        callback(null)
        document.body.removeChild(overlay)
    })

    inputElement.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            document.getElementById('customPromptOk').click()
        }
    })
} 