window.applyLayerDefaults = function(layerId) {
    const defaultColor = "#4BDC13";
    const defaultSymbol = layerId.toUpperCase();
    const defaultResource = "points";

    if (layers[layerId]) {
        layers[layerId].color = layers[layerId].color || defaultColor;
        layers[layerId].symbol = layers[layerId].symbol || defaultSymbol;
        layers[layerId].resource = layers[layerId].resource || defaultResource;
        // Add more defaults as needed
    }

    if (tmp[layerId]) {
        tmp[layerId].color = tmp[layerId].color || defaultColor;
        tmp[layerId].symbol = tmp[layerId].symbol || defaultSymbol;
        tmp[layerId].resource = tmp[layerId].resource || defaultResource;
    }

    // Reapply node style after defaults
    tmp[layerId].nodeStyle = getNodeStyle(layerId);
} 