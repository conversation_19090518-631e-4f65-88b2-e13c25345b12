// Utility functions for modifying basic information and updating the UI

// Update basic information and refresh the UI
function updateBasicInfo(key, value) {
    if (!player.mod) {
        player.mod = {
            unlocked: true,
            points: new Decimal(0),
            gameTitle: modInfo.name,
            pointsName: modInfo.pointsName,
            startPoints: modInfo.initialStartPoints,
            discordLink: modInfo.discordLink,
            discordName: modInfo.discordName,
            author: modInfo.author,
            exportString: "",
            layers: ["p"],
            nextLayerId: 1,
            selectedLayer: "p",
            version: VERSION.num,
            versionName: VERSION.name,
            changelog: "No changes logged yet.",
            winText: "Congratulations! You beat the game!",
            offlineLimit: 1
        }
    }

    // Update the value in player.mod
    player.mod[key] = value

    // Update modInfo and VERSION as needed
    switch (key) {
        case 'gameTitle':
            modInfo.name = value
            document.title = `${modInfo.name} ${VERSION.num}`
            break
        case 'pointsName':
            modInfo.pointsName = value
            break
        case 'startPoints':
            modInfo.initialStartPoints = new Decimal(value)
            break
        case 'discordLink':
            modInfo.discordLink = value
            break
        case 'discordName':
            modInfo.discordName = value
            break
        case 'author':
            modInfo.author = value
            break
        case 'version':
            VERSION.num = value
            document.title = `${modInfo.name} ${VERSION.num}`
            break
        case 'versionName':
            VERSION.name = value
            break
        case 'winText':
            modInfo.winText = value
            break
        case 'offlineLimit':
            modInfo.offlineLimit = value
            break
    }

    // Save the changes
    saveModData()
    
    // Force UI update
    needCanvasUpdate = true
}

// Update a layer's basic information
function updateLayerInfo(layerId, key, value) {
    if (!layers[layerId]) return
    
    // Update the layer property
    layers[layerId][key] = value
    
    // Update tmp data
    if (tmp[layerId]) {
        tmp[layerId][key] = value
    }
    
    // Save the changes
    saveModData()
    
    // Force UI update
    needCanvasUpdate = true
} 