// js/features/milestoneEditor.js

function getMilestoneEditorHTML(layer, id) {
    id = String(id);
    let milestone = layers[layer].milestones[id]
    if (!milestone) return ''

    return `
    <h3>Edit Milestone</h3>
    <div class="editor-section">
        <h4>Basic Properties</h4>
        <div class="grid grid-2col">
            <div class="form-group">
                <label for="req-desc-is-function">Requirement Description:</label>
                <input type="checkbox" id="req-desc-is-function" ${milestone.reqDescIsFunction ? 'checked' : ''}> Is Function
                <div id="req-desc-content">
                    ${milestone.reqDescIsFunction ? `<textarea id="milestone-requirement" placeholder="Enter requirement function code">${milestone.requirementCode || ''}</textarea>` : `<input type="text" id="milestone-requirement" value="${milestone.requirementDescription || ''}" placeholder="Enter requirement description">`}
                </div>
            </div>
            <div class="form-group">
                <label for="effect-desc-is-function">Effect Description:</label>
                <input type="checkbox" id="effect-desc-is-function" ${milestone.effectDescIsFunction ? 'checked' : ''}> Is Function
                <div id="effect-desc-content">
                    ${milestone.effectDescIsFunction ? `<textarea id="milestone-effect-description" placeholder="Enter effect function code">${milestone.effectCode || ''}</textarea>` : `<textarea id="milestone-effect-description" placeholder="Enter effect description">${milestone.effectDescription || ''}</textarea>`}
                </div>
            </div>
            <div class="form-group">
                <label for="milestone-tooltip">Tooltip:</label>
                <input type="text" id="milestone-tooltip" value="${milestone.tooltip || ''}" placeholder="Enter tooltip">
            </div>
        </div>
    </div>
    
    <div class="editor-section">
        <h4>Completion</h4>
        <div class="grid grid-2col">
            <div class="form-group">
                <label for="done-type">Done When:</label>
                <select id="done-type">
                    <option value="always" ${!milestone.doneCondition ? 'selected' : ''}>Always</option>
                    <option value="points" ${milestone.doneCondition === 'points' ? 'selected' : ''}>Points Reached</option>
                    <option value="upgrades" ${milestone.doneCondition === 'upgrades' ? 'selected' : ''}>Upgrades Purchased</option>
                    <option value="custom" ${milestone.doneCondition === 'custom' ? 'selected' : ''}>Custom Condition</option>
                </select>
            </div>
            <div id="done-value-container" style="display: ${milestone.doneCondition && milestone.doneCondition !== 'always' && milestone.doneCondition !== 'custom' ? 'block' : 'none'}">
                <label for="done-value">Required Value:</label>
                <input type="text" id="done-value" value="${milestone.doneValue || ''}" placeholder="Enter required value">
            </div>
            <div id="custom-done-container" style="display: ${milestone.doneCondition === 'custom' ? 'block' : 'none'}">
                <label for="custom-done">Custom Done Condition:</label>
                <textarea id="custom-done" placeholder="Enter custom done code (return true/false)">${milestone.customDone || ''}</textarea>
                <small class="small-text">Return true/false to determine if milestone is done</small>
            </div>
            <div class="form-group">
                <label for="milestone-oncomplete">On Complete Code:</label>
                <textarea id="milestone-oncomplete" placeholder="Enter on complete code">${milestone.onCompleteCode || ''}</textarea>
            </div>
        </div>
    </div>
    
    <div class="editor-section">
        <h4>Toggles</h4>
        <div id="toggles-container">
            ${(milestone.toggles || []).map((toggle, index) => `
                <div class="grid grid-3col" style="margin-bottom: 10px;">
                    <input type="text" value="${toggle[0]}" placeholder="Layer">
                    <input type="text" value="${toggle[1]}" placeholder="Variable">
                    <button onclick="removeToggle(${index})">Remove</button>
                </div>
            `).join('')}
        </div>
        <button onclick="addToggle()">Add Toggle</button>
    </div>
    
    <div class="editor-section">
        <h4>Visibility</h4>
        <div class="form-group">
            <label for="milestone-unlocked">Unlocked Code:</label>
            <textarea id="milestone-unlocked" placeholder="Enter unlocked code">${milestone.unlockedCode || 'return true'}</textarea>
        </div>
    </div>
    
    <div class="editor-section">
        <h4>Style</h4>
        <div class="grid grid-2col">
            <div class="form-group">
                <label for="milestone-bg-color">Background Color:</label>
                <input type="color" id="milestone-bg-color" value="${milestone.style?.background || '#000000'}">
            </div>
            <div class="form-group">
                <label for="milestone-font-color">Font Color:</label>
                <input type="color" id="milestone-font-color" value="${milestone.style?.color || '#FFFFFF'}">
            </div>
            <div class="form-group">
                <label for="milestone-font-size">Font Size:</label>
                <input type="number" id="milestone-font-size" value="${milestone.style?.fontSize || 16}" min="1">
            </div>
            <div class="form-group">
                <label for="milestone-font-weight">Font Weight:</label>
                <input type="text" id="milestone-font-weight" value="${milestone.style?.fontWeight || 'normal'}" placeholder="e.g. bold">
            </div>
            <div class="form-group">
                <label for="milestone-font-style">Font Style:</label>
                <input type="text" id="milestone-font-style" value="${milestone.style?.fontStyle || 'normal'}" placeholder="e.g. italic">
            </div>
            <div class="form-group">
                <label for="milestone-text-align">Text Align:</label>
                <select id="milestone-text-align">
                    <option value="left" ${milestone.style?.textAlign === 'left' ? 'selected' : ''}>Left</option>
                    <option value="center" ${milestone.style?.textAlign === 'center' ? 'selected' : ''}>Center</option>
                    <option value="right" ${milestone.style?.textAlign === 'right' ? 'selected' : ''}>Right</option>
                </select>
            </div>
            <div class="form-group">
                <label for="milestone-padding">Padding:</label>
                <input type="number" id="milestone-padding" value="${milestone.style?.padding || 10}" min="0">
            </div>
            <div class="form-group">
                <label for="milestone-margin">Margin:</label>
                <input type="number" id="milestone-margin" value="${milestone.style?.margin || 10}" min="0">
            </div>
            <div class="form-group">
                <label for="milestone-border-radius">Border Radius:</label>
                <input type="number" id="milestone-border-radius" value="${milestone.style?.borderRadius || 0}" min="0">
            </div>
            <div class="form-group">
                <label for="milestone-border">Border:</label>
                <input type="text" id="milestone-border" value="${milestone.style?.border || 'none'}" placeholder="e.g. 1px solid #fff">
            </div>
            <div class="form-group">
                <label for="milestone-box-shadow">Box Shadow:</label>
                <input type="text" id="milestone-box-shadow" value="${milestone.style?.boxShadow || 'none'}" placeholder="e.g. 0 0 10px #000">
            </div>
        </div>
    </div>
    
    <div class="editor-buttons">
        <button class="cancel-button" onclick="closeFeatureEditor()">Cancel</button>
        <button class="save-button" onclick="saveMilestoneEdit('${layer}', '${id}')">Save Changes</button>
    </div>
`
}

// Placeholder function, the real logic will be in openFeatureEditor in featureManagement.js
function editMilestone(layerId, milestoneId) {
    console.log(`editMilestone called for layer: ${layerId}, milestone: ${milestoneId}`);
    // This function will be called by openFeatureEditor, which will handle the overlay
}

function closeMilestoneEditor() {
    closeFeatureEditor(); // Use the centralized close function
}

function saveMilestoneEdit(layer, id) {
    let milestone = layers[layer].milestones[id]
    if (!milestone) return
    
    // Basic
    const reqIsFunc = document.getElementById('req-desc-is-function').checked
    milestone.reqDescIsFunction = reqIsFunc
    if (reqIsFunc) {
        milestone.requirementCode = document.getElementById('milestone-requirement').value
        milestone.requirementDescription = new Function(milestone.requirementCode)
    } else {
        milestone.requirementDescription = document.getElementById('milestone-requirement').value
    }
    
    const effectIsFunc = document.getElementById('effect-desc-is-function').checked
    milestone.effectDescIsFunction = effectIsFunc
    if (effectIsFunc) {
        milestone.effectCode = document.getElementById('milestone-effect-description').value
        milestone.effectDescription = new Function(milestone.effectCode)
    } else {
        milestone.effectDescription = document.getElementById('milestone-effect-description').value
    }
    
    milestone.tooltip = document.getElementById('milestone-tooltip').value
    
    // Completion
    milestone.doneCondition = document.getElementById('done-type').value
    if (milestone.doneCondition !== 'always' && milestone.doneCondition !== 'custom') {
        milestone.doneValue = document.getElementById('done-value').value || '0'
    }
    if (milestone.doneCondition === 'custom') {
        milestone.customDone = document.getElementById('custom-done').value || 'return false'
    }

    // Generate done function
    let doneCode = ''
    switch(milestone.doneCondition) {
        case 'points':
            doneCode = `return player.points.gte(new Decimal('${milestone.doneValue || 0}'))`
            break
        case 'upgrades':
            doneCode = `return (player['${layer}'].upgrades || []).length >= ${milestone.doneValue || 0}`
            break
        case 'custom':
            doneCode = milestone.customDone || 'return false'
            break
        default:
            doneCode = 'return false'
    }
    
    try {
        milestone.done = new Function(doneCode)
    } catch(e) {
        console.error('Error creating done function:', e)
        milestone.done = () => false
    }

    // On Complete
    milestone.onCompleteCode = document.getElementById('milestone-oncomplete').value
    if (milestone.onCompleteCode) milestone.onComplete = new Function(milestone.onCompleteCode)

    // Toggles
    milestone.toggles = [] // Collect from inputs
    document.querySelectorAll('#toggles-container .milestone-toggle-input').forEach(input => {
        const layer = input.querySelector('input[type="text"]:nth-child(1)').value
        const variable = input.querySelector('input[type="text"]:nth-child(2)').value
        milestone.toggles.push([layer, variable])
    })

    // Visibility
    milestone.unlockedCode = document.getElementById('milestone-unlocked').value
    milestone.unlocked = new Function(milestone.unlockedCode)
    
    // Style
    milestone.style = {
        background: document.getElementById('milestone-bg-color').value,
        color: document.getElementById('milestone-font-color').value,
        fontSize: document.getElementById('milestone-font-size').value,
        fontWeight: document.getElementById('milestone-font-weight').value,
        fontStyle: document.getElementById('milestone-font-style').value,
        textAlign: document.getElementById('milestone-text-align').value,
        padding: document.getElementById('milestone-padding').value,
        margin: document.getElementById('milestone-margin').value,
        borderRadius: document.getElementById('milestone-border-radius').value,
        border: document.getElementById('milestone-border').value,
        boxShadow: document.getElementById('milestone-box-shadow').value
    }

    // Save layer configuration
    saveLayerConfig(layer)

    // Force update
    if (!window.isUpdating) {
        window.isUpdating = true
        updateTemp()
        updateTabFormats()
        window.isUpdating = false
    }

    closeFeatureEditor(); // Use the centralized close function
    openFeatureEditor(layer, 'milestones'); // Re-open the feature list to show updated content
} 