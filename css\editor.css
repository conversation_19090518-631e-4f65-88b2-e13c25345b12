/* editor.css */

.feature-editor-dialog {
  background: rgba(20, 20, 20, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 15px;
  padding: 15px; /* Further reduced padding */
  width: 95%;
  max-width: 1100px; /* Slightly reduced max-width */
  position: relative;
  box-shadow: 0 0 20px rgba(0,0,0,0.5);
  color: #ffffff;
  font-family: 'Orbitron', sans-serif;
  max-height: 85vh; /* Reduced max-height */
  overflow-y: auto; /* Keep auto, aim to make content fit */
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px; /* Reduced margin */
}

.editor-header h2 {
  margin: 0;
  font-size: 20px; /* Reduced font size */
}

.close-button {
  background: none;
  border: none;
  color: white;
  font-size: 20px; /* Reduced font size */
  cursor: pointer;
  transition: color 0.3s;
}

.close-button:hover {
  color: #ff4444;
}

.editor-section {
  margin: 10px 0; /* Further reduced margin */
  padding: 8px; /* Further reduced padding */
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
}

.editor-section h4 {
  margin-bottom: 8px; /* Reduced margin */
  font-size: 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding-bottom: 3px; /* Reduced padding */
}

.grid {
  display: grid;
  gap: 8px; /* Reduced gap */
}

.grid-2col {
  grid-template-columns: repeat(2, 1fr);
}

.grid-3col {
  grid-template-columns: repeat(3, 1fr);
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  margin-bottom: 2px; /* Reduced margin */
  font-size: 12px; /* Reduced font size */
  font-weight: bold;
}

input[type="text"],
input[type="number"],
textarea,
select {
  width: 100%;
  padding: 5px; /* Reduced padding */
  background: rgba(0, 0, 0, 0.3);
  color: #ffffff;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 5px;
  font-size: 12px; /* Reduced font size */
  transition: border-color 0.3s;
}

input[type="text"]:focus,
input[type="number"]:focus,
textarea:focus,
select:focus {
  border-color: #4CAF50;
  outline: none;
}

textarea {
  height: 45px; /* Further reduced height */
  resize: vertical;
}

input[type="checkbox"] {
  margin-right: 5px;
}

input[type="color"] {
  height: 20px; /* Reduced height */
  padding: 0;
}

.editor-buttons {
  margin-top: 15px; /* Reduced margin */
  text-align: right;
  display: flex;
  justify-content: flex-end;
  gap: 8px; /* Reduced gap */
}

.cancel-button {
  padding: 6px 12px; /* Reduced padding */
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: white;
  cursor: pointer;
  border-radius: 5px;
  transition: background 0.3s;
}

.cancel-button:hover {
  background: rgba(255, 255, 255, 0.2);
}

.save-button {
  padding: 6px 12px; /* Reduced padding */
  background: #4CAF50;
  border: none;
  color: white;
  cursor: pointer;
  border-radius: 5px;
  transition: background 0.3s;
}

.save-button:hover {
  background: #45a049;
}

.small-text {
  color: #888;
  font-size: 10px; /* Reduced font size */
  margin-top: 2px; /* Reduced margin */
}

.no-features-message {
  text-align: center;
  padding: 15px; /* Reduced padding */
  color: #aaa;
  font-size: 0.9em; /* Reduced font size */
  border: 1px dashed rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  margin: 10px 0; /* Reduced margin */
  background: rgba(255, 255, 255, 0.03);
}

.feature-list-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(260px, 1fr)); /* Adjusted minmax */
  gap: 15px; /* Reduced gap */
  padding: 8px; /* Reduced padding */
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  box-shadow: inset 0 0 10px rgba(0,0,0,0.3);
}

.feature-list-item {
  background: rgba(40, 40, 40, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 8px; /* Reduced border-radius */
  padding: 12px; /* Reduced padding */
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.feature-list-item:hover {
  transform: translateY(-2px); /* Reduced translateY */
  box-shadow: 0 4px 10px rgba(0,0,0,0.4); /* Reduced box-shadow */
}

.feature-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px; /* Reduced margin */
  padding-bottom: 5px; /* Reduced padding */
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.feature-item-header h4 {
  margin: 0;
  font-size: 1.1em; /* Reduced font size */
  color: #fff;
}

.feature-item-actions {
  display: flex;
  gap: 6px; /* Reduced gap */
}

.edit-button, .delete-button {
  padding: 5px 10px; /* Reduced padding */
  border-radius: 5px; /* Reduced border-radius */
  cursor: pointer;
  font-size: 0.7em; /* Reduced font size */
  display: flex;
  align-items: center;
  gap: 5px; /* Reduced gap */
  transition: all 0.3s ease;
  border: none;
  color: white;
}

.edit-button {
  background: rgba(75, 220, 19, 0.3);
  border: 1px solid rgba(75, 220, 19, 0.5);
}

.edit-button:hover {
  background: rgba(75, 220, 19, 0.5);
  border-color: rgba(75, 220, 19, 0.8);
  transform: translateY(-1px);
}

.delete-button {
  background: rgba(220, 19, 19, 0.3);
  border: 1px solid rgba(220, 19, 19, 0.5);
}

.delete-button:hover {
  background: rgba(220, 19, 19, 0.5);
  border-color: rgba(220, 19, 19, 0.8);
  transform: translateY(-1px);
}

.feature-item-details p {
  font-size: 0.8em; /* Reduced font size */
  margin-bottom: 5px; /* Reduced margin */
} 