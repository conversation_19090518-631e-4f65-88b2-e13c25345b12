function getMechanicsHTML(layer) {
    return `
    <div style='
        background: rgba(255, 255, 255, 0.05);
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 20px;
    '>
        <h4 style='margin-bottom: 15px;'>Layer Mechanics</h4>
        <div style='
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
        '>
            <div class='mechanic-button' style='position: relative;'>
                <button onclick='window.editLayerMechanic("type")' style='
                    background: rgba(255, 255, 255, 0.1);
                    border: none;
                    padding: 10px;
                    border-radius: 8px;
                    color: white;
                    cursor: pointer;
                    transition: all 0.3s;
                    width: 100%;
                    text-align: left;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                '>
                    <span><i class="fas fa-cog"></i> Type: ${layer.type || "normal"}</span>
                    <i class="fas fa-question-circle info-icon"></i>
                </button>
                <div class='tooltip-text'>${getLayerTooltip("type")}</div>
            </div>
            <div class='mechanic-button' style='position: relative;'>
                <button onclick='window.editLayerMechanic("resource")' style='
                    background: rgba(255, 255, 255, 0.1);
                    border: none;
                    padding: 10px;
                    border-radius: 8px;
                    color: white;
                    cursor: pointer;
                    transition: all 0.3s;
                    width: 100%;
                    text-align: left;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                '>
                    <span><i class="fas fa-coins"></i> Resource: ${layer.resource}</span>
                    <i class="fas fa-question-circle info-icon"></i>
                </button>
                <div class='tooltip-text'>${getLayerTooltip("resource")}</div>
            </div>
            <div class='mechanic-button' style='position: relative;'>
                <button onclick='window.editLayerMechanic("requires")' style='
                    background: rgba(255, 255, 255, 0.1);
                    border: none;
                    padding: 10px;
                    border-radius: 8px;
                    color: white;
                    cursor: pointer;
                    transition: all 0.3s;
                    width: 100%;
                    text-align: left;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                '>
                    <span><i class="fas fa-unlock"></i> Required: ${layer.requires}</span>
                    <i class="fas fa-question-circle info-icon"></i>
                </button>
                <div class='tooltip-text'>${getLayerTooltip("requires")}</div>
            </div>
            <div class='mechanic-button' style='position: relative;'>
                <button onclick='window.editLayerMechanic("resetDescription")' style='
                    background: rgba(255, 255, 255, 0.1);
                    border: none;
                    padding: 10px;
                    border-radius: 8px;
                    color: white;
                    cursor: pointer;
                    transition: all 0.3s;
                    width: 100%;
                    text-align: left;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                '>
                    <span><i class="fas fa-sync"></i> Prestige Text: ${layer.resetDescription || "Reset for "}</span>
                    <i class="fas fa-question-circle info-icon"></i>
                </button>
                <div class='tooltip-text'>The text shown on the prestige button</div>
            </div>
        </div>
    </div>`
} 