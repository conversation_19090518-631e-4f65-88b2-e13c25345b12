.barBase {
	overflow: hidden;
	-webkit-mask-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAIAAACQd1PeAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAA5JREFUeNpiYGBgAAgwAAAEAAGbA+oJAAAAAElFTkSuQmCC);
	display:table
}

.barBorder {
	border: 2px solid;
	border-radius: 10px;
	border-color: var(--color);
	overflow: hidden;
	-webkit-mask-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAIAAACQd1PeAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAA5JREFUeNpiYGBgAAgwAAAEAAGbA+oJAAAAAElFTkSuQmCC);
	margin:0
}

.overlayTextContainer {
	z-index: 3;
	border-radius: 10px;
	vertical-align: middle;
	display: flex;
	justify-content: center;
	align-items: left;
	position: absolute;
}

.fill {
	background-color: var(--color);
	z-index:2;
    position: absolute;
	overflow: hidden;
	margin-left: -0.5px;
	transition-duration: 0.2s;

}

.overlayText {
	z-index: 6;
}