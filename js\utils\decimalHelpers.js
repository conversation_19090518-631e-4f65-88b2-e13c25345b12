function ensureDecimal(value) {
    if (value === undefined || value === null) return new Decimal(1)
    if (value instanceof Decimal) return value
    if (typeof value === 'number' || typeof value === 'string') return new Decimal(value)
    if (typeof value === 'function') {
        try {
            let result = value()
            return result instanceof Decimal ? result : new Decimal(result)
        } catch(e) {
            console.error('Error evaluating function:', e)
            return new Decimal(1)
        }
    }
    return new Decimal(1)
} 