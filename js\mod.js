let modInfo = {
	name: "The Super Modding Tree",
	id: "supermoddingtree",
	author: "You",
	pointsName: "points",
	modFiles: ["layers.js", "tree.js"],
	discordName: "",
	discordLink: "",
	initialStartPoints: new Decimal(10),
	offlineLimit: 1
}

// Set your version in num and name
let VERSION = {
	num: "0.0",
	name: "Initial Release",
}

let changelog = `<h1>Changelog:</h1><br>
	<h3>v0.0</h3><br>
		- Added things.<br>
		- Added stuff.`

let winText = `Congratulations! You have reached the end and beaten this game, but for now...`

// If you add new functions anywhere inside of a layer, and those functions have an effect when called, add them here.
// (The ones here are examples, all official functions are already taken care of)
var doNotCallTheseFunctionsEveryTick = ["blowUpEverything"]

function getStartPoints(){
    // First check player.mod.startPoints
    if (player.mod && player.mod.startPoints) {
        let points = new Decimal(player.mod.startPoints)
        if (points.eq(0)) {
            return new Decimal(modInfo.initialStartPoints)
        }
        return points
    }
    // Then check modInfo.initialStartPoints
    if (modInfo.initialStartPoints) {
        return new Decimal(modInfo.initialStartPoints)
    }
    // Default fallback
    return new Decimal(10)
}

// Determines if it should show points/sec
function canGenPoints(){
	return true
}

// Calculate points/sec!
function getPointGen() {
	if(!canGenPoints())
		return new Decimal(0)

	let gain = new Decimal(1)
	return gain
}

// You can add non-layer related variables that should to into "player" and be saved here, along with default values
function addedPlayerData() { return {
}}

// Display extra things at the top of the page
var displayThings = [
]

// Determines when the game "ends"
function isEndgame() {
	return player.points.gte(new Decimal("e280000000"))
}



// Less important things beyond this point!

// Style for the background, can be a function
var backgroundStyle = {

}

// You can change this if you have things that can be messed up by long tick lengths
function maxTickLength() {
	return(3600) // Default is 1 hour which is just arbitrarily large
}

// Use this if you need to undo inflation from an older version. If the version is older than the version that fixed the issue,
// you can cap their current resources with this.
function fixOldSave(oldVersion){
}

// Add function to update version info
function updateVersionInfo() {
    if (player?.mod) {
        // Update VERSION object
        VERSION.num = player.mod.version || "0.0"
        VERSION.name = player.mod.versionName || "Initial Release"
        
        // Update player version to match
        player.version = VERSION.num
        
        // Update modInfo with loaded values to ensure they're used in the UI
        modInfo.name = player.mod.gameTitle || modInfo.name
        modInfo.pointsName = player.mod.pointsName || modInfo.pointsName
        modInfo.initialStartPoints = new Decimal(player.mod.startPoints || modInfo.initialStartPoints)
        modInfo.discordLink = player.mod.discordLink || modInfo.discordLink
        modInfo.discordName = player.mod.discordName || modInfo.discordName
        modInfo.author = player.mod.author || modInfo.author
        modInfo.offlineLimit = player.mod.offlineLimit || modInfo.offlineLimit
        
        // Update document title
        document.title = `${modInfo.name} ${VERSION.num}`
        
        // Update VERSION.withoutName and VERSION.withName
        VERSION.withoutName = "v" + VERSION.num + (VERSION.pre ? " Pre-Release " + VERSION.pre : VERSION.pre ? " Beta " + VERSION.beta : "")
        VERSION.withName = VERSION.withoutName + (VERSION.name ? ": " + VERSION.name : "")
        
        // Force canvas update to refresh version display
        needCanvasUpdate = true
        
        // Save to localStorage to persist changes
        saveModData()
    }
}

// Add function to save mod data
function saveModData() {
    let modConfig = {
        gameTitle: modInfo.name,
        pointsName: modInfo.pointsName,
        startPoints: modInfo.initialStartPoints,
        discordLink: modInfo.discordLink,
        discordName: modInfo.discordName,
        author: modInfo.author,
        layers: player.mod.layers,
        nextLayerId: player.mod.nextLayerId,
        selectedLayer: player.mod.selectedLayer,
        version: modInfo.version,
        versionName: modInfo.versionName,
        changelog: modInfo.changelog,
        winText: modInfo.winText,
        offlineLimit: modInfo.offlineLimit
    }
    localStorage.setItem("modConfig", JSON.stringify(modConfig))

    let layerConfig = {}
    for (let layerId in layers) {
        let layer = layers[layerId]
        if (layer.isModded) {
            layerConfig[layerId] = {
                name: layer.name,
                color: layer.color,
                symbol: layer.symbol,
                symbolIsImage: layer.symbolIsImage,
                symbolImage: layer.symbolImage,
                symbolPixelPerfect: layer.symbolPixelPerfect,
                symbolFont: layer.symbolFont,
                type: layer.type,
                resource: layer.resource,
                baseResource: layer.baseResource,
                requires: layer.requires.toString(),
                exponent: layer.exponent.toString(),
                gainMult: typeof layer.gainMult === 'function' ? layer.gainMult.toString() : 'function() { return new Decimal(1) }',
                gainExp: typeof layer.gainExp === 'function' ? layer.gainExp.toString() : 'function() { return new Decimal(1) }',
                row: layer.row,
                upgrades: layer.upgrades || {},
                milestones: layer.milestones || {},
                challenges: layer.challenges || {}
            }
        }
    }
    localStorage.setItem("layerConfig", JSON.stringify(layerConfig))
}

// Add function to load mod data
function loadModData() {
    let savedConfig = localStorage.getItem("modConfig")
    if (savedConfig) {
        let config = JSON.parse(savedConfig)
        player.mod = {
            unlocked: true,
            points: new Decimal(0),
            gameTitle: config.gameTitle || modInfo.name,
            pointsName: config.pointsName || modInfo.pointsName,
            startPoints: config.startPoints || modInfo.initialStartPoints,
            discordLink: config.discordLink || modInfo.discordLink,
            discordName: config.discordName || modInfo.discordName,
            author: config.author || modInfo.author,
            exportString: "",
            layers: (config.layers || ["p"]).map(String),
            nextLayerId: config.nextLayerId || 1,
            selectedLayer: (String(config.selectedLayer) === "[object Object]" ? "p" : String(config.selectedLayer)) || "p",
            version: config.version || "0.0",
            versionName: config.versionName || "Initial Release",
            changelog: config.changelog || "No changes logged yet.",
            winText: config.winText || "Congratulations! You beat the game!",
            offlineLimit: config.offlineLimit || 1
        }
        
        // Update modInfo with loaded values
        updateVersionInfo()

        // Load layer configurations
        let layerConfig = localStorage.getItem("layerConfig")
        if (layerConfig) {
            layerConfig = JSON.parse(layerConfig)
            for (let layerId in layerConfig) {
                if (!layers[layerId]) continue
                let config = layerConfig[layerId]
                
                // Apply saved properties
                layers[layerId].name = config.name
                layers[layerId].color = config.color
                layers[layerId].symbol = config.symbol
                layers[layerId].symbolIsImage = config.symbolIsImage
                layers[layerId].symbolImage = config.symbolImage
                layers[layerId].symbolPixelPerfect = config.symbolPixelPerfect
                layers[layerId].symbolFont = config.symbolFont
                layers[layerId].type = config.type
                layers[layerId].resource = config.resource
                layers[layerId].baseResource = config.baseResource
                layers[layerId].requires = new Decimal(config.requires)
                layers[layerId].exponent = new Decimal(config.exponent)
                
                // Properly handle function properties to ensure they return Decimal objects
                try {
                    layers[layerId].gainMult = new Function('return ' + config.gainMult)()
                    let originalGainMult = layers[layerId].gainMult
                    layers[layerId].gainMult = function() {
                        let result = originalGainMult.call(this)
                        return result instanceof Decimal ? result : new Decimal(result)
                    }
                } catch(e) {
                    layers[layerId].gainMult = function() { return new Decimal(1) }
                }
                
                try {
                    layers[layerId].gainExp = new Function('return ' + config.gainExp)()
                    let originalGainExp = layers[layerId].gainExp
                    layers[layerId].gainExp = function() {
                        let result = originalGainExp.call(this)
                        return result instanceof Decimal ? result : new Decimal(result)
                    }
                } catch(e) {
                    layers[layerId].gainExp = function() { return new Decimal(1) }
                }
                
                // Ensure baseAmount returns a Decimal
                let originalBaseAmount = layers[layerId].baseAmount
                layers[layerId].baseAmount = function() {
                    let result = originalBaseAmount.call(this)
                    return result instanceof Decimal ? result : new Decimal(result)
                }
                
                layers[layerId].row = config.row
                layers[layerId].upgrades = config.upgrades || {}
                layers[layerId].milestones = config.milestones || {}
                layers[layerId].challenges = config.challenges || {}

                // Update temporary data
                if (!tmp[layerId]) tmp[layerId] = {}
                Object.assign(tmp[layerId], layers[layerId])
                
                // Ensure tmp values are Decimals
                if (tmp[layerId]) {
                    tmp[layerId].gainExp = layers[layerId].gainExp()
                    tmp[layerId].gainMult = layers[layerId].gainMult()
                    tmp[layerId].baseAmount = layers[layerId].baseAmount()
                    tmp[layerId].requires = new Decimal(layers[layerId].requires)
                    tmp[layerId].exponent = new Decimal(layers[layerId].exponent)
                }
            }
        }
    }
}

// Update VERSION after loading mod data
// window.addEventListener('load', function() {
//     if (player?.mod) {
//         VERSION.num = player.mod.version || "0.0"
//         VERSION.name = player.mod.versionName || "Initial Release"
//     }
// })

// Override hardReset function to preserve mod data
function hardReset(resetModData=false) {
    let oldModData
    if (!resetModData) {
        oldModData = JSON.parse(JSON.stringify(player.mod)) // Deep copy to prevent reference issues
    }
    
    if (tmp.gameEnded && !player.keepGoing) {
        player.keepGoing = true
        needCanvasUpdate = true
    }
    
    player = getStartPlayer()
    if (!resetModData && oldModData) {
        player.mod = oldModData
        
        // Ensure starting points are applied correctly
        if (player.mod.startPoints) {
            player.points = new Decimal(player.mod.startPoints)
            modInfo.initialStartPoints = new Decimal(player.mod.startPoints)
        }
        
        // Ensure version info is applied correctly
        if (player.mod.version) {
            VERSION.num = player.mod.version
            player.version = player.mod.version
        }
        
        if (player.mod.versionName) {
            VERSION.name = player.mod.versionName
        }
        
        // Update other modInfo properties
        if (player.mod.gameTitle) modInfo.name = player.mod.gameTitle
        if (player.mod.pointsName) modInfo.pointsName = player.mod.pointsName
        if (player.mod.discordLink) modInfo.discordLink = player.mod.discordLink
        if (player.mod.discordName) modInfo.discordName = player.mod.discordName
        if (player.mod.author) modInfo.author = player.mod.author
        if (player.mod.offlineLimit) modInfo.offlineLimit = player.mod.offlineLimit
        
        // Update document title
        document.title = `${modInfo.name} ${VERSION.num}`
    }
    
    loadGame()
    if (!resetModData) {
        saveModData()
        updateVersionInfo()
        needCanvasUpdate = true
    }

    // These overrides need to be done after the game loads
    // window.addEventListener('load', function() {
    //     // Store original functions
    //     const originalDoReset = window.doReset
    //     const originalLoadGame = window.loadGame
    //     
    //     // Override doReset
    //     window.doReset = function(layer, force=false) {
    //         saveModData()
    //         return originalDoReset(layer, force)
    //     }
    //     
    //     // Override loadGame
    //     window.loadGame = function() {
    //         let result = originalLoadGame()
    //         // Removed calls to loadModData, loadLayerConfigurations, and recreateDynamicLayers here
    //         return result
    //     }
    // })
}