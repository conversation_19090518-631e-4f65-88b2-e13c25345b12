# 2.0 format changes

- Temp format is changed from `temp.something[layer]` to `temp[layer].something`, for consistency
- Challenges are now saved as an object with the amount of completions in each spot. (This will break saves.)
- `effectDisplay` in Challenges and Upgrades no longer takes an argument, and neither does `effect` for Buyables
- Buyable cost can take an argument for amount of buyables, but it needs to function if no argument is supplied (it should do the cost for the next purchase).
- Generation of Points now happens in the main game loop (not in a layer update function), enabled by `canGenPoints` in [game.js](js/game.js).
- Changed `fullLayerReset` to `layerDataReset`, which takes an array of names of values to keep

In addition, many names were changed, mostly expanding abbreviations:

All instances of: 
- chall -> challenge
- unl -> unlocked
- upg -> upgrade (besides CSS)
- amt -> amount
- desc -> description
- resCeil -> roundUpCost
- order -> unlockOrder
- incr_order -> increaseUnlockOrder

Challenges:
- desc -> challengeDescription
- reward -> rewardDescription
- effect -> rewardEffect
- effectDisplay -> rewardDisplay
- active -> challengeActive
