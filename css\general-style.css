@font-face {
	font-family: 'Orbitron';
	src: url('../resources/Orbitron-Medium.ttf') format('truetype');
}

/* Global things */
body {
	color: var(--color);
	overflow: hidden;
	--background: #0f0f0f;
	--color: #dfdfdf;
	--points: #ffffff;
	background: var(--background);
	font-family: "Inconsolata", monospace;
}

/* General text */
h1, h2, h3, b, input {
	display: inline;
	font-family: "Inconsolata", monospace;
}

/* These are styles for different states of components. You can make layer-specific versions with .c.locked, for example */
.locked {
	background-color: #bf8f8f;
	cursor: not-allowed;
}

/* Can meens can be clicked/bought/etc */
.can {
	cursor: pointer;
}

.can:hover {
	transform: scale(1.15, 1.15);
	box-shadow: 0 0 20px var(--points)
}

.bought {
	background-color: #77bf5f;
	cursor: default;
}

#points {
	color: var(--points);
	text-shadow: 0 0 10px var(--points);
}

.layer-btn {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    padding: 10px 15px;
    border-radius: 8px;
    color: var(--color);
    cursor: pointer;
    transition: all 0.3s;
    font-size: 14px;
}

.layer-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.feature-btn {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    padding: 15px;
    border-radius: 12px;
    color: white;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

.feature-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.feature-icon {
    font-size: 24px;
}

.feature-text {
    font-size: 14px;
    opacity: 0.9;
}

.layer-card:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
}

.layer-card.active {
    background: rgba(255, 255, 255, 0.15);
}

/* Mod-specific elements use Orbitron */
.mod-text {
    font-family: 'Orbitron', sans-serif;
}

/* Tooltip and Info Icon Styles */
.info-icon {
    margin-left: 5px;
    position: relative;
    opacity: 0.7;
    transition: opacity 0.3s;
}

.info-icon:hover {
    opacity: 1;
}

.tooltip-text {
    display: none;
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.95);
    padding: 8px 12px;
    border-radius: 5px;
    font-size: 12px;
    width: max-content;
    max-width: 250px;
    z-index: 1000;
    margin-bottom: 5px;
    color: #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    pointer-events: none;
    transition: opacity 0.3s;
}

.mechanic-button .info-icon:hover + .tooltip-text {
    display: block;
}

/* Feature Card Styles */
.feature-card {
    background: rgba(30, 30, 30, 0.4) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    border-radius: 10px !important;
    padding: 20px !important;
    margin: 0 !important;
    height: fit-content !important;
}

.feature-card:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
}

.feature-card h4 {
    margin: 0 0 10px 0;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.feature-card button {
    position: relative !important;
    z-index: 2 !important;
    background: rgba(255, 255, 255, 0.1) !important;
    border: none !important;
    padding: 10px 15px !important;
    border-radius: 8px !important;
    color: white !important;
    cursor: pointer !important;
    transition: all 0.3s !important;
}

.feature-card button:hover {
    background: rgba(75, 220, 19, 0.3) !important;
    transform: translateY(-2px) !important;
}

/* Feature Editor Overlay */
.feature-editor-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    background: none !important;
    z-index: 100000 !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
}

.feature-editor-dialog {
    background: rgba(20, 20, 20, 0.4) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    padding: 20px !important;
    width: 95% !important;
    height: auto !important;
    max-height: 90vh !important;
    max-width: 1600px !important;
    display: flex !important;
    flex-direction: column !important;
    border-radius: 10px !important;
}

.feature-editor-content {
    padding: 20px !important;
    margin-top: 15px !important;
    overflow-x: hidden !important;
    overflow-y: auto !important;
    max-height: calc(90vh - 120px) !important;
}

.feature-editor-header {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    padding: 10px !important;
    background: rgba(0, 0, 0, 0.2) !important;
    border-radius: 8px !important;
    margin-bottom: 15px !important;
}

.feature-editor-header button {
    background: rgba(75, 220, 19, 0.2) !important;
    border: 1px solid rgba(75, 220, 19, 0.4) !important;
    color: white !important;
    padding: 8px 15px !important;
    border-radius: 5px !important;
    cursor: pointer !important;
    transition: all 0.3s !important;
}

.feature-editor-header button:hover {
    background: rgba(75, 220, 19, 0.3) !important;
    transform: translateY(-2px) !important;
}

.feature-editor-close {
    background: rgba(255, 255, 255, 0.1) !important;
    border: none !important;
    padding: 10px !important;
    border-radius: 50% !important;
    color: white !important;
    cursor: pointer !important;
    transition: all 0.3s !important;
    width: 40px !important;
    height: 40px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.feature-editor-close:hover {
    background: rgba(255, 0, 0, 0.3) !important;
    transform: rotate(90deg) !important;
}

/* Feature Items */
.upgrade-item,
.milestone-item,
.challenge-item {
    background: rgba(40, 40, 40, 0.4) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    border-radius: 8px !important;
    padding: 15px !important;
    margin-bottom: 10px !important;
    transition: all 0.3s !important;
}

.upgrade-item:hover,
.milestone-item:hover,
.challenge-item:hover {
    background: rgba(255, 255, 255, 0.1) !important;
    transform: translateX(5px) !important;
}

.upgrade-name,
.milestone-name,
.challenge-name {
    font-size: 16px !important;
    font-weight: bold !important;
    margin-bottom: 5px !important;
    color: #fff !important;
}

.upgrade-desc,
.milestone-desc,
.challenge-desc {
    font-size: 14px !important;
    color: rgba(255, 255, 255, 0.7) !important;
    line-height: 1.4 !important;
}

.layer-features-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(450px, 1fr)) !important;
    gap: 20px !important;
    padding: 0 !important;
    margin: 0 !important;
    width: 100% !important;
}

/* Remove any backdrop filters */
* {
    backdrop-filter: none !important;
}

/* Override Basic Information container */
[data-tab="Basic Information"],
[data-tab="Basic Information"] > div,
[data-tab="Basic Information"] > div > div {
    background: none !important;
    box-shadow: none !important;
    border: none !important;
    padding: 0 !important;
    margin: 0 !important;
    border-radius: 0 !important;
}
