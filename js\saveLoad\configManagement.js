// Save layer configuration to localStorage
function saveLayerConfig(layerId) {
    let layer = layers[layerId]
    if (!layer) return
    
    // Get layer config from localStorage
    let layerConfig = localStorage.getItem("layerConfig") ? JSON.parse(localStorage.getItem("layerConfig")) : {}
    
    // Update layer config
    if (!layerConfig[layerId]) {
        layerConfig[layerId] = {}
    }
    
    // Save all layer properties
    layerConfig[layerId] = {
        name: layer.name,
        color: layer.color,
        symbol: layer.symbol,
        symbolIsImage: layer.symbolIsImage,
        symbolImage: layer.symbolImage,
        symbolPixelPerfect: layer.symbolPixelPerfect,
        symbolFont: layer.symbolFont,
        type: layer.type,
        resource: layer.resource,
        baseResource: layer.baseResource,
        requires: layer.requires ? layer.requires.toString() : undefined,
        exponent: layer.exponent ? layer.exponent.toString() : undefined,
        gainMult: typeof layer.gainMult === 'function' ? layer.gainMult.toString() : undefined,
        gainExp: typeof layer.gainExp === 'function' ? layer.gainExp.toString() : undefined,
        resetDescription: layer.resetDescription || "Reset for ",
        row: layer.row,
        displayRow: layer.displayRow,
        position: layer.position,
        layerShown: typeof layer.layerShown === 'function' ? layer.layerShown.toString() : undefined,
    }

    // Save upgrades, milestones, and challenges separately, ensuring functions are stringified
    if (layer.upgrades) {
        layerConfig[layerId].upgrades = JSON.parse(JSON.stringify(layer.upgrades, (key, value) => {
            if (typeof value === 'function') {
                return value.toString()
            } else if (value instanceof Decimal) {
                return value.toString()
            }
            return value
        }))
    }
    if (layer.milestones) {
        layerConfig[layerId].milestones = JSON.parse(JSON.stringify(layer.milestones, (key, value) => {
            if (typeof value === 'function') {
                return value.toString()
            } else if (value instanceof Decimal) {
                return value.toString()
            }
            return value
        }))
    }
    if (layer.challenges) {
        layerConfig[layerId].challenges = JSON.parse(JSON.stringify(layer.challenges, (key, value) => {
            if (typeof value === 'function') {
                return value.toString()
            } else if (value instanceof Decimal) {
                return value.toString()
            }
            return value
        }))
    }
    
    // Save to localStorage
    localStorage.setItem("layerConfig", JSON.stringify(layerConfig))
}

// Function to load layer configuration on game load
function loadLayerConfigurations() {
    try {
        let layerConfig = localStorage.getItem("layerConfig")
        if (layerConfig) {
            layerConfig = JSON.parse(layerConfig)
            for (let layerId in layerConfig) {
                if (!layers[layerId]) {
                    continue
                }
                let config = layerConfig[layerId]
                
                // Apply basic properties
                Object.assign(layers[layerId], {
                    name: config.name,
                    color: config.color,
                    symbol: config.symbol,
                    symbolIsImage: config.symbolIsImage,
                    symbolImage: config.symbolImage,
                    symbolPixelPerfect: config.symbolPixelPerfect,
                    symbolFont: config.symbolFont,
                    type: config.type,
                    resource: config.resource,
                    baseResource: config.baseResource
                })
                
                // Handle numeric values
                if (config.requires) layers[layerId].requires = new Decimal(config.requires)
                if (config.exponent) layers[layerId].exponent = new Decimal(config.exponent)
                
                // Apply features
                layers[layerId].upgrades = config.upgrades || {}
                layers[layerId].milestones = config.milestones || {}
                layers[layerId].challenges = config.challenges || {}
            }
        }
    } catch(e) {
        console.error('Error in loadLayerConfigurations:', e)
    }
}

// Helper function to initialize layer functions
function initializeLayerFunctions(layerId) {
    if (!layers[layerId]) return
    
    // Set default functions if they don't exist or need to be overridden by loaded data
    if (!layers[layerId].gainMult || typeof layers[layerId].gainMult !== 'function') {
        layers[layerId].gainMult = function() { return new Decimal(1) }
    }
    if (!layers[layerId].gainExp || typeof layers[layerId].gainExp !== 'function') {
        layers[layerId].gainExp = function() { return new Decimal(1) }
    }
    if (!layers[layerId].baseAmount || typeof layers[layerId].baseAmount !== 'function') {
        layers[layerId].baseAmount = function() { return player.points }
    }
    
    // Initialize prestige button text function
    if (!layers[layerId].prestigeButtonText) {
        layers[layerId].prestigeButtonText = function() {
            return (this.resetDescription || "Reset for ") + formatWhole(tmp[layerId].resetGain) + " " + this.resource
        }
    }
    
    // Initialize canReset function if not exists
    if (!layers[layerId].canReset) {
        layers[layerId].canReset = function() {
            return tmp[layerId].baseAmount.gte(tmp[layerId].requires)
        }
    }
    
    // Initialize getResetGain function if not exists
    if (!layers[layerId].getResetGain) {
        layers[layerId].getResetGain = function() {
            if (!this.canReset()) return new Decimal(0)
            let gain = tmp[layerId].baseAmount.div(tmp[layerId].requires).pow(tmp[layerId].exponent)
            gain = gain.times(tmp[layerId].gainMult)
            gain = gain.pow(tmp[layerId].gainExp)
            return gain.floor()
        }
    }
}

// Function to re-create dynamic layers from saved configuration
function recreateDynamicLayers() {
    if (player.mod && player.mod.layers) {
        for (let i = 0; i < player.mod.layers.length; i++) {
            let layerId = player.mod.layers[i]
            if (layerId === 'p' || layerId === 'mod') continue // Skip predefined layers

            let savedLayerData = JSON.parse(localStorage.getItem("layerConfig"))[layerId]
            if (savedLayerData) {
                // Reconstruct functions from string representations if needed
                if (typeof savedLayerData.gainMult === 'string') {
                    savedLayerData.gainMult = new Function(savedLayerData.gainMult)
                }
                if (typeof savedLayerData.gainExp === 'string') {
                    savedLayerData.gainExp = new Function(savedLayerData.gainExp)
                }
                if (typeof savedLayerData.baseAmount === 'string') {
                    savedLayerData.baseAmount = new Function(savedLayerData.baseAmount)
                }
                if (typeof savedLayerData.canReset === 'string') {
                    savedLayerData.canReset = new Function(savedLayerData.canReset)
                }
                if (typeof savedLayerData.layerShown === 'string') {
                    savedLayerData.layerShown = new Function(savedLayerData.layerShown)
                }
                if (typeof savedLayerData.getResetGain === 'string') {
                    savedLayerData.getResetGain = new Function(savedLayerData.getResetGain)
                }
                if (typeof savedLayerData.prestigeButtonText === 'string') {
                    savedLayerData.prestigeButtonText = new Function(savedLayerData.prestigeButtonText)
                }
                // Reconstruct feature functions
                for (let featureType of ['upgrades', 'milestones', 'challenges']) {
                    if (savedLayerData[featureType]) {
                        for (let id in savedLayerData[featureType]) {
                            let feature = savedLayerData[featureType][id];
                            if (feature.effect && typeof feature.effect === 'string') {
                                feature.effect = new Function('value', feature.effect);
                            }
                            if (feature.effectDisplay && typeof feature.effectDisplay === 'string') {
                                feature.effectDisplay = new Function('value', feature.effectDisplay);
                            }
                            if (feature.done && typeof feature.done === 'string') {
                                feature.done = new Function(feature.done);
                            }
                            if (feature.unlocked && typeof feature.unlocked === 'string') {
                                feature.unlocked = new Function(feature.unlocked);
                            }
                            if (feature.rewardEffect && typeof feature.rewardEffect === 'string') {
                                feature.rewardEffect = new Function('value', feature.rewardEffect);
                            }
                            if (feature.rewardDisplay && typeof feature.rewardDisplay === 'string') {
                                feature.rewardDisplay = new Function('value', feature.rewardDisplay);
                            }
                        }
                    }
                }

                addLayer(layerId, savedLayerData)
            } else {
            }
        }
    } else {
    }
}

window.addEventListener('load', () => {
    const originalLoadGame = window.loadGame;
    window.loadGame = function() {
        let result = originalLoadGame();
        loadModData();
        loadLayerConfigurations();
        recreateDynamicLayers();
        return result;
    };
});