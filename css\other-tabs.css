
#optionWheel {
	position: absolute;
	top: 0;
	left: 0;
	height: 50px;
	width: 50px;
	cursor: pointer;
}

#optionWheel:hover {
	transform: rotate(360deg);
}

#info {
	font-size: 20px;
	color: white;
	position: absolute;
	top: 50px;
	left: 4px;
	cursor: pointer;
	width: 40px;
	height: 40px;
	-webkit-text-stroke-width: 1px;
	-webkit-text-stroke-color: #02f2f2;
}

#info:hover {
	transform: scale(1.2, 1.2);
	text-shadow: 5px 0 10px #02f2f2,
              -3px 0 12px #02f2f2;
}

#discord {
	position: absolute;
	top: 120px;
	left: 4px;
	width: 40px;
	height: 40px;
}

#discord img {
	width: 100%;
	height: 100%;
}

#discord-links {
	position: absolute;
	top: 0;
	padding-top: 44px;
	left: -244px;
	width: 200px;
	transition: left .3s ease;
}

#discord:hover #discord-links {
	left: -4px;
}

#version {
	position: absolute;
	right: 4px;
	top: 4px;
	text-align: right;
	color: var(--points);
	text-shadow: 0 0 10px var(--points);
	cursor: pointer;
}

#version:hover {
	transform: scale(1.1, 1.1);
	right: 4.8px;
}

a {
	color: #007fff;
	text-decoration-line: none;
	cursor: pointer
}

.link {
	display: block;
	font-size: 20px;
	color: #41f5f5;
	cursor: pointer;
	font-family: "Lucida Console", "Courier New", monospace;
	-webkit-text-stroke-width: 1px;
	-webkit-text-stroke-color: #02f2f2;
	text-decoration: none;
}

.link:hover {
	transform: scale(1.2, 1.2);
	text-shadow: 5px 0 10px #02f2f2,
              -3px 0 12px #02f2f2;
}

.opt {
	height: 100px;
	width: 100px;
	border-radius: 25%;
	border: 4px solid;
	background-color: var(--color);
	border-color: rgba(0, 0, 0, 0.125);
	color: rgba(0, 0, 0, 0.5);
	cursor: pointer;
}

.opt:hover {
	background-color: #439ea3;
}