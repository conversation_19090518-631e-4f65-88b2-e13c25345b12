// js/features/challengeEditor.js

function getChallengeEditorHTML(layer, id) {
    id = String(id);
    let challenge = layers[layer].challenges[id]
    if (!challenge) return ''

    return `
    <h3>Edit Challenge</h3>
    <div class="editor-section">
        <h4>Basic Properties</h4>
        <div class="grid grid-2col">
            <div class="form-group">
                <label for="goal-is-function">Goal:</label>
                <input type="checkbox" id="goal-is-function" ${challenge.goalIsFunction ? 'checked' : ''}> Is Function
                <div id="goal-content">
                    ${challenge.goalIsFunction ? `<textarea id="challenge-goal" placeholder="Enter goal function code">${challenge.goalCode || ''}</textarea>` : `<input type="number" id="challenge-goal" value="${challenge.goal || 0}" placeholder="Enter goal value">`}
                </div>
            </div>
            <div class="form-group">
                <label for="reward-desc-is-function">Reward Description:</label>
                <input type="checkbox" id="reward-desc-is-function" ${challenge.rewardDescIsFunction ? 'checked' : ''}> Is Function
                <div id="reward-desc-content">
                    ${challenge.rewardDescIsFunction ? `<textarea id="challenge-reward-description" placeholder="Enter reward description function code">${challenge.rewardDescCode || ''}</textarea>` : `<textarea id="challenge-reward-description" placeholder="Enter reward description">${challenge.rewardDescription || ''}</textarea>`}
                </div>
            </div>
        </div>
    </div>
    
    <div class="editor-section">
        <h4>Reward</h4>
        <div class="grid grid-2col">
            <div class="form-group">
                <label for="challenge-reward-effect">Reward Effect Code:</label>
                <textarea id="challenge-reward-effect" placeholder="Enter reward effect code">${challenge.rewardEffectCode || ''}</textarea>
            </div>
            <div class="form-group">
                <label for="challenge-reward-display">Reward Display Code:</label>
                <textarea id="challenge-reward-display" placeholder="Enter reward display code">${challenge.rewardDisplayCode || ''}</textarea>
            </div>
        </div>
    </div>
    
    <div class="editor-section">
        <h4>Events</h4>
        <div class="grid grid-3col">
            <div class="form-group">
                <label for="challenge-on-enter">On Enter Code:</label>
                <textarea id="challenge-on-enter" placeholder="Enter on enter code">${challenge.onEnterCode || ''}</textarea>
            </div>
            <div class="form-group">
                <label for="challenge-on-exit">On Exit Code:</label>
                <textarea id="challenge-on-exit" placeholder="Enter on exit code">${challenge.onExitCode || ''}</textarea>
            </div>
            <div class="form-group">
                <label for="challenge-on-complete">On Complete Code:</label>
                <textarea id="challenge-on-complete" placeholder="Enter on complete code">${challenge.onCompleteCode || ''}</textarea>
            </div>
        </div>
    </div>

    <div class="editor-section">
        <h4>Visibility & Limits</h4>
        <div class="grid grid-2col">
            <div class="form-group">
                <label for="challenge-unlocked">Unlocked Code:</label>
                <textarea id="challenge-unlocked" placeholder="Enter unlocked condition code">${challenge.unlockedCode || 'return true'}</textarea>
            </div>
            <div class="form-group">
                <label for="challenge-completion-limit">Completion Limit:</label>
                <input type="number" id="challenge-completion-limit" value="${challenge.completionLimit || 1}" min="1">
            </div>
        </div>
    </div>
    
    <div class="editor-section">
        <h4>Style</h4>
        <div class="grid grid-2col">
            <div class="form-group">
                <label for="challenge-bg-color">Background Color:</label>
                <input type="color" id="challenge-bg-color" value="${challenge.style?.background || '#000000'}">
            </div>
            <div class="form-group">
                <label for="challenge-text-color">Text Color:</label>
                <input type="color" id="challenge-text-color" value="${challenge.style?.color || '#ffffff'}">
            </div>
            // Add more style fields as needed
        </div>
    </div>
    
    <div class="editor-buttons">
        <button class="cancel-button" onclick="closeFeatureEditor()">Cancel</button>
        <button class="save-button" onclick="saveChallengeEdit('${layer}', '${id}')">Save Changes</button>
    </div>
`
}

// Placeholder function, the real logic will be in openFeatureEditor in featureManagement.js
function editChallenge(layerId, challengeId) {
    console.log(`editChallenge called for layer: ${layerId}, challenge: ${challengeId}`);
    // This function will be called by openFeatureEditor, which will handle the overlay
}

function closeChallengeEditor() {
    closeFeatureEditor(); // Use the centralized close function
}

function saveChallengeEdit(layer, id) {
    let challenge = layers[layer].challenges[id]
    if (!challenge) return
    
    // Basic
    const goalIsFunc = document.getElementById('goal-is-function').checked
    challenge.goalIsFunction = goalIsFunc
    if (goalIsFunc) {
        challenge.goalCode = document.getElementById('challenge-goal').value
        challenge.goal = new Function(challenge.goalCode)
    } else {
        challenge.goal = new Decimal(document.getElementById('challenge-goal').value)
    }
    
    const rewardDescIsFunc = document.getElementById('reward-desc-is-function').checked
    challenge.rewardDescIsFunction = rewardDescIsFunc
    if (rewardDescIsFunc) {
        challenge.rewardDescCode = document.getElementById('challenge-reward-description').value
        challenge.rewardDescription = new Function(challenge.rewardDescCode)
    } else {
        challenge.rewardDescription = document.getElementById('challenge-reward-description').value
    }
    
    // Reward
    challenge.rewardEffectCode = document.getElementById('challenge-reward-effect').value
    if (challenge.rewardEffectCode) challenge.rewardEffect = new Function(challenge.rewardEffectCode)
    
    challenge.rewardDisplayCode = document.getElementById('challenge-reward-display').value
    if (challenge.rewardDisplayCode) challenge.rewardDisplay = new Function(challenge.rewardDisplayCode)
    
    // Events
    challenge.onEnterCode = document.getElementById('challenge-on-enter').value
    if (challenge.onEnterCode) challenge.onEnter = new Function(challenge.onEnterCode)
    
    challenge.onExitCode = document.getElementById('challenge-on-exit').value
    if (challenge.onExitCode) challenge.onExit = new Function(challenge.onExitCode)
    
    challenge.onCompleteCode = document.getElementById('challenge-on-complete').value
    if (challenge.onCompleteCode) challenge.onComplete = new Function(challenge.onCompleteCode)
    
    // Visibility & Limits
    challenge.unlockedCode = document.getElementById('challenge-unlocked').value
    challenge.unlocked = new Function(challenge.unlockedCode)
    
    challenge.completionLimit = parseInt(document.getElementById('challenge-completion-limit').value) || 1
    
    // Style
    challenge.style = {
        background: document.getElementById('challenge-bg-color').value || '#000000',
        color: document.getElementById('challenge-text-color').value || '#ffffff',
        // Add more
    }
    
    // Save
} 