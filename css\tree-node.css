/* Tree nodes, button nodes, and side layers */
.treeNode {
	height: 100px;
	width: 100px;
	border: var(--hqProperty1);
	border-color: rgba(0, 0, 0, 0.125);
	border-radius: 50%;
	box-shadow: var(--hqProperty2a), var(--hqProperty2b);
	font-size: 40px;
	font-family: "Lucida Console", "Courier New", monospace;
	color: rgba(0, 0, 0, 0.5);
	text-shadow: var(--hqProperty3);
	padding: 0;
	margin: 0 10px 0 10px;
}

.nodeLabel {
	font: inherit;
	font-family: inherit;
}

.treeButton {
	height: 100px;
	width: 100px;
	border: var(--hqProperty1);
	border-color: rgba(0, 0, 0, 0.125);
	border-radius: 33%;
	box-shadow: var(--hqProperty2a), var(--hqProperty2b);
	font-size: 40px;
	font-family: "<PERSON><PERSON> Console", "Courier New", monospace;
	color: rgba(0, 0, 0, 0.5);
	text-shadow: var(--hqProperty3);
	padding: 0;
}

.smallNode {
	height: 60px;
	width: 60px;
	font-size: 30px;
}

.resetNotify {
	box-shadow: var(--hqProperty2a), 0 0 8px #ffffff;
}

.treeNode.can:hover {
	transform: scale(1.15, 1.15);
	box-shadow: var(--hqProperty2a), 0 0 20px var(--points);
}

.notify {
	transform: scale(1.05, 1.05);
	border-color: rgba(0, 0, 0, 0.125);
	box-shadow: var(--hqProperty2a), 0 0 20px #ff0000;
}



.mark {
	position: relative;
	display: inline-block;
	width: 30px;
	height: 30px;
	z-index: 10000;
	margin-left: 0.9em;
	margin-right: 0.9em;
	margin-bottom: 1.2em;
	border-right: 0.3em solid transparent;
	border-bottom: 0.7em solid transparent;
	border-left: 0.3em solid transparent;
	font-size: 10px;
	overflow:auto;
	pointer-events: none;
}

.star {
	position: relative;
	display: inline-block;
	width: 0;
	height: 0;
	z-index: 10000;
	margin-left: 0.9em;
	margin-right: 0.9em;
	margin-bottom: 1.2em;
	border-right: 0.3em solid transparent;
	border-bottom: 0.7em solid #ffcc00;
	border-left: 0.3em solid transparent;
	font-size: 10px;
	pointer-events: none;

}

.star:before, .star:after {
	content: "";
    width: 0;
    height: 0;
    position: absolute;
    top: .6em;
    left: -1em;
	border-right: 1em solid transparent;
    border-bottom: 0.7em solid #ffcc00;
    border-left: 1em solid transparent;
	transform: rotate(-35deg);
}

.star:after {
	transform: rotate(35deg);
}