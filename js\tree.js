var layoutInfo = {
    startTab: "none",
    startNavTab: "tree-tab",
    showTree: true,
    treeLayout: [
        ["p"],
        [],  // Empty row for spacing
        ["mod"] // Mod node on its own row
    ]
}



// A "ghost" layer which offsets other layers in the tree
addNode("blank", {
    layerShown: "ghost",
}, 
)


addLayer("tree-tab", {
    tabFormat: [["tree", function() {return (layoutInfo.treeLayout ? layoutInfo.treeLayout : TREE_LAYERS)}]],
    previousTab: "",
    leftTab: true,
})