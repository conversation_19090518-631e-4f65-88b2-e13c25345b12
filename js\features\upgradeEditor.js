// Define boost types and their functions
const boostTypes = {
    'add': {
        name: 'Add',
        description: 'Add a fixed amount',
        generateFunction: (power) => `return value.plus(new Decimal('${power || 1}'))`
    },
    'multiply': {
        name: 'Multiply',
        description: 'Multiply by a fixed amount',
        generateFunction: (power) => `return value.times(new Decimal('${power || 1}'))`
    },
    'power': {
        name: 'Power',
        description: 'Raise to a power',
        generateFunction: (power) => `return value.pow(new Decimal('${power || 1}'))`
    },
    'custom': {
        name: 'Custom Formula',
        description: 'Use a custom formula',
        generateFunction: (formula) => `return ${formula}`
    }
}

function getUpgradeEditorHTML(layer, id) {
    console.log(`getUpgradeEditorHTML: Called with layer:`, layer, `id: ${id}, type: ${typeof id}`);
    id = String(id);
    let upgrade = layers[layer].upgrades[id]
    if (!upgrade) {
        console.error(`getUpgradeEditorHTML: Upgrade '${id}' for layer '${layer}' is undefined or layers[layer].upgrades is undefined.`);
        return ''
    }
    
    return `
    <h3>Edit Upgrade</h3>
    <div class="editor-section">
        <h4>Basic Properties</h4>
        <div class="grid grid-2col">
            <div class="form-group">
                <label for="title-is-function">Title:</label>
                <input type="checkbox" id="title-is-function" ${upgrade.titleIsFunction ? 'checked' : ''}> Is Function
                <div id="title-content">
                    ${upgrade.titleIsFunction ? `<textarea id="upgrade-title" placeholder="Enter title function code">${upgrade.titleCode || ''}</textarea>` : `<input type="text" id="upgrade-title" value="${upgrade.title || ''}" placeholder="Enter title">`}
                </div>
            </div>
            <div class="form-group">
                <label for="description-is-function">Description:</label>
                <input type="checkbox" id="description-is-function" ${upgrade.descriptionIsFunction ? 'checked' : ''}> Is Function
                <div id="description-content">
                    ${upgrade.descriptionIsFunction ? `<textarea id="upgrade-description" placeholder="Enter description function code">${upgrade.descriptionCode || ''}</textarea>` : `<textarea id="upgrade-description" placeholder="Enter description">${upgrade.description || ''}</textarea>`}
                </div>
            </div>
            <div class="form-group">
                <label for="upgrade-cost">Cost:</label>
                <input type="text" id="upgrade-cost" value="${upgrade.cost || '1'}" placeholder="Enter cost">
            </div>
            <div class="form-group">
                <label for="upgrade-tooltip">Tooltip:</label>
                <input type="text" id="upgrade-tooltip" value="${upgrade.tooltip || ''}" placeholder="Enter tooltip">
            </div>
        </div>
    </div>
    
    <div class="editor-section">
        <h4>Effect</h4>
        <div class="grid grid-2col">
            <div class="form-group">
                <label for="effect-type">Effect Type:</label>
                <select id="effect-type">
                    <option value="add" ${upgrade.effectType === 'add' ? 'selected' : ''}>Add</option>
                    <option value="multiply" ${upgrade.effectType === 'multiply' ? 'selected' : ''}>Multiply</option>
                    <option value="power" ${upgrade.effectType === 'power' ? 'selected' : ''}>Power</option>
                    <option value="custom" ${upgrade.effectType === 'custom' ? 'selected' : ''}>Custom Formula</option>
                </select>
            </div>
            <div class="form-group">
                <label for="effect-power">Effect Power:</label>
                <input type="text" id="effect-power" value="${upgrade.effectPower || '2'}" placeholder="Enter effect power">
            </div>
            <div id="custom-effect-container" style="display: ${upgrade.effectType === 'custom' ? 'block' : 'none'}; grid-column: span 2;">
                <label for="custom-effect">Custom Effect Formula:</label>
                <textarea id="custom-effect" placeholder="Enter custom effect formula (use 'value')">${upgrade.customEffect || ''}</textarea>
                <small class="small-text">Use 'value' as the input variable</small>
            </div>
            <div class="form-group" style="grid-column: span 2;">
                <label for="upgrade-effectdisplay">Effect Display Code:</label>
                <textarea id="upgrade-effectdisplay" placeholder="Enter effect display code">${upgrade.effectDisplayCode || ''}</textarea>
            </div>
        </div>
    </div>
    
    <div class="editor-section">
        <h4>Visibility & Unlocking</h4>
        <div class="grid grid-2col">
            <div class="form-group">
                <label for="upgrade-max">Maximum Purchases:</label>
                <input type="number" id="upgrade-max" value="${upgrade.maxPurchases || '1'}" min="1" placeholder="Enter max purchases">
            </div>
            <div class="form-group">
                <label for="unlock-type">Unlocked When:</label>
                <select id="unlock-type">
                    <option value="always" ${!upgrade.unlockCondition ? 'selected' : ''}>Always</option>
                    <option value="points" ${upgrade.unlockCondition === 'points' ? 'selected' : ''}>Points Reached</option>
                    <option value="upgrades" ${upgrade.unlockCondition === 'upgrades' ? 'selected' : ''}>Upgrades Purchased</option>
                    <option value="custom" ${upgrade.unlockCondition === 'custom' ? 'selected' : ''}>Custom Condition</option>
                </select>
            </div>
            <div id="unlock-value-container" style="display: ${upgrade.unlockCondition && upgrade.unlockCondition !== 'always' && upgrade.unlockCondition !== 'custom' ? 'block' : 'none'}">
                <label for="unlock-value">Required Value:</label>
                <input type="text" id="unlock-value" value="${upgrade.unlockValue || ''}" placeholder="Enter required value">
            </div>
            <div id="custom-unlock-container" style="display: ${upgrade.unlockCondition === 'custom' ? 'block' : 'none'}">
                <label for="custom-unlock">Custom Unlock Condition:</label>
                <textarea id="custom-unlock" placeholder="Enter custom unlock code (return true/false)">${upgrade.customUnlock || ''}</textarea>
                <small class="small-text">Return true/false to determine if upgrade is unlocked</small>
            </div>
        </div>
    </div>
    
    <div class="editor-section">
        <h4>Currency</h4>
        <div class="grid grid-2col">
            <div class="form-group">
                <label for="upgrade-currencydisplay">Currency Display Name:</label>
                <input type="text" id="upgrade-currencydisplay" value="${upgrade.currencyDisplayName || ''}" placeholder="Enter currency display name">
            </div>
            <div class="form-group">
                <label for="upgrade-currencyinternal">Currency Internal Name:</label>
                <input type="text" id="upgrade-currencyinternal" value="${upgrade.currencyInternalName || ''}" placeholder="Enter currency internal name">
            </div>
            <div class="form-group">
                <label for="upgrade-currencylayer">Currency Layer:</label>
                <input type="text" id="upgrade-currencylayer" value="${upgrade.currencyLayer || ''}" placeholder="Enter currency layer">
            </div>
            <div class="form-group" style="grid-column: span 2;">
                <label for="upgrade-currencylocation">Currency Location Code:</label>
                <textarea id="upgrade-currencylocation" placeholder="Enter currency location code">${upgrade.currencyLocationCode || ''}</textarea>
            </div>
        </div>
    </div>
    
    <div class="editor-section">
        <h4>Advanced</h4>
        <div class="grid grid-2col">
            <div class="form-group">
                <label for="upgrade-onpurchase">On Purchase Code:</label>
                <textarea id="upgrade-onpurchase" placeholder="Enter on purchase code">${upgrade.onPurchaseCode || ''}</textarea>
            </div>
            <div class="form-group">
                <label for="upgrade-canafford">Can Afford Code:</label>
                <textarea id="upgrade-canafford" placeholder="Enter can afford code">${upgrade.canAffordCode || ''}</textarea>
            </div>
            <div class="form-group">
                <label for="upgrade-pay">Pay Code:</label>
                <textarea id="upgrade-pay" placeholder="Enter pay code">${upgrade.payCode || ''}</textarea>
            </div>
            <div class="form-group">
                <label for="upgrade-fulldisplay">Full Display Code:</label>
                <textarea id="upgrade-fulldisplay" placeholder="Enter full display code">${upgrade.fullDisplayCode || ''}</textarea>
            </div>
        </div>
    </div>
    
    <div class="editor-section">
        <h4>Style</h4>
        <div class="grid grid-2col">
            <div class="form-group">
                <label for="upgrade-bg-color">Background Color:</label>
                <input type="color" id="upgrade-bg-color" value="${upgrade.style?.background || '#000000'}">
            </div>
            <div class="form-group">
                <label for="upgrade-border-color">Border Color:</label>
                <input type="color" id="upgrade-border-color" value="${upgrade.style?.border || '#ffffff'}">
            </div>
            <div class="form-group">
                <label for="upgrade-text-color">Text Color:</label>
                <input type="color" id="upgrade-text-color" value="${upgrade.style?.color || '#ffffff'}">
            </div>
            <div class="form-group">
                <label for="upgrade-font-size">Font Size:</label>
                <input type="text" id="upgrade-font-size" value="${upgrade.style?.['font-size'] || '14px'}" placeholder="Enter font size (e.g. 14px)">
            </div>
        </div>
    </div>
    
    <div class="editor-buttons">
        <button class="cancel-button" onclick="closeFeatureEditor()">Cancel</button>
        <button class="save-button" onclick="saveUpgradeEdit('${layer}', '${id}')">Save Changes</button>
    </div>
`
}

// Placeholder function, the real logic will be in openFeatureEditor in featureManagement.js
function editUpgrade(layerId, upgradeId) {
    console.log(`editUpgrade called for layer: ${layerId}, upgrade: ${upgradeId}`);
    // This function will be called by openFeatureEditor, which will handle the overlay
}

function closeUpgradeEditor() {
    closeFeatureEditor(); // Use the centralized close function
}

function saveUpgradeEdit(layer, id) {
    let upgrade = layers[layer].upgrades[id]
    if (!upgrade) return
    
    // Basic
    const titleIsFunction = document.getElementById('title-is-function').checked
    upgrade.titleIsFunction = titleIsFunction
    if (titleIsFunction) {
        upgrade.titleCode = document.getElementById('upgrade-title').value
        try { upgrade.title = new Function(upgrade.titleCode) } catch(e) { console.error(e) }
    } else {
        upgrade.title = document.getElementById('upgrade-title').value
    }
    
    const descIsFunction = document.getElementById('description-is-function').checked
    upgrade.descriptionIsFunction = descIsFunction
    if (descIsFunction) {
        upgrade.descriptionCode = document.getElementById('upgrade-description').value
        try { upgrade.description = new Function(upgrade.descriptionCode) } catch(e) { console.error(e) }
    } else {
        upgrade.description = document.getElementById('upgrade-description').value
    }
    
    upgrade.cost = new Decimal(document.getElementById('upgrade-cost').value || 0)
    upgrade.tooltip = document.getElementById('upgrade-tooltip').value || ''
    
    // Effect
    upgrade.effectType = document.getElementById('effect-type').value
    upgrade.effectPower = new Decimal(document.getElementById('effect-power').value || 1)
    if (upgrade.effectType === 'custom') {
        upgrade.customEffect = document.getElementById('custom-effect').value || ''
    }
    upgrade.effectDisplayCode = document.getElementById('upgrade-effectdisplay').value || ''
    if (upgrade.effectDisplayCode) {
        try { upgrade.effectDisplay = new Function(upgrade.effectDisplayCode) } catch(e) { console.error(e) }
    } else {
        // Generate effect display function
        upgrade.effectDisplay = function() {
            let displayCode = ''
            switch(upgrade.effectType) {
                case 'add':
                    displayCode = `return '+${format(this.effectPower)}'`
                    break
                case 'multiply':
                    displayCode = `return '×${format(this.effectPower)}'`
                    break
                case 'power':
                    displayCode = `return '↑${format(this.effectPower)}'`
                    break
                case 'custom':
                    displayCode = upgrade.customEffectDisplay || 'return ""' // Add customEffectDisplay property
                    break
                default:
                    displayCode = 'return ""'
            }
            try {
                // Create a function that takes 'this' (the upgrade object) and 'value' as arguments
                const func = new Function('value', displayCode);
                return func.call(this, this.effect()); // Pass the current effect value as 'value'
            } catch(e) {
                console.error('Error creating effect display function:', e)
                return "Error"
            }
        }
    }
    
    // Visibility
    upgrade.maxPurchases = parseInt(document.getElementById('upgrade-max').value || 1)
    upgrade.unlockCondition = document.getElementById('unlock-type').value
    if (upgrade.unlockCondition !== 'always' && upgrade.unlockCondition !== 'custom') {
        upgrade.unlockValue = document.getElementById('unlock-value').value || '0'
    }
    if (upgrade.unlockCondition === 'custom') {
        upgrade.customUnlock = document.getElementById('custom-unlock').value || 'return true'
    }
    
    // Currency
    upgrade.currencyDisplayName = document.getElementById('upgrade-currencydisplay').value || ''
    upgrade.currencyInternalName = document.getElementById('upgrade-currencyinternal').value || ''
    upgrade.currencyLayer = document.getElementById('upgrade-currencylayer').value || ''
    upgrade.currencyLocationCode = document.getElementById('upgrade-currencylocation').value || ''
    if (upgrade.currencyLocationCode) {
        try { upgrade.currencyLocation = new Function(upgrade.currencyLocationCode) } catch(e) { console.error(e) }
    }
    
    // Advanced
    upgrade.onPurchaseCode = document.getElementById('upgrade-onpurchase').value || ''
    if (upgrade.onPurchaseCode) {
        try { upgrade.onPurchase = new Function(upgrade.onPurchaseCode) } catch(e) { console.error(e) }
    }
    upgrade.canAffordCode = document.getElementById('upgrade-canafford').value || ''
    if (upgrade.canAffordCode) {
        try { upgrade.canAfford = new Function(upgrade.canAffordCode) } catch(e) { console.error(e) }
    }
    upgrade.payCode = document.getElementById('upgrade-pay').value || ''
    if (upgrade.payCode) {
        try { upgrade.pay = new Function(upgrade.payCode) } catch(e) { console.error(e) }
    }
    upgrade.fullDisplayCode = document.getElementById('upgrade-fulldisplay').value || ''
    if (upgrade.fullDisplayCode) {
        try { upgrade.fullDisplay = new Function(upgrade.fullDisplayCode) } catch(e) { console.error(e) }
    }
    
    // Style
    upgrade.style = {
        background: document.getElementById('upgrade-bg-color').value || '#000000',
        border: document.getElementById('upgrade-border-color').value || '#ffffff',
        color: document.getElementById('upgrade-text-color').value || '#ffffff',
        'font-size': document.getElementById('upgrade-font-size').value || '14px'
        // Add more
    }
    
    // Generate effect function
    let effectCode = ''
    switch(upgrade.effectType) {
        case 'add':
            effectCode = boostTypes.add.generateFunction(upgrade.effectPower)
            break
        case 'multiply':
            effectCode = boostTypes.multiply.generateFunction(upgrade.effectPower)
            break
        case 'power':
            effectCode = boostTypes.power.generateFunction(upgrade.effectPower)
            break
        case 'custom':
            effectCode = upgrade.customEffect || 'return value'
            break
        default:
            effectCode = 'return new Decimal(1)'
    }
    
    try {
        upgrade.effect = new Function('value', effectCode)
    } catch(e) {
        console.error('Error creating effect function:', e)
        upgrade.effect = () => new Decimal(1)
    }
    
    // Generate unlock function
    let unlockCode = ''
    switch(upgrade.unlockCondition) {
        case 'points':
            unlockCode = `return player.points.gte(new Decimal('${upgrade.unlockValue || 0}'))`
            break
        case 'upgrades':
            unlockCode = `return (player['${layer}'].upgrades || []).length >= ${upgrade.unlockValue || 0}`
            break
        case 'custom':
            unlockCode = upgrade.customUnlock || 'return true'
            break
        default:
            unlockCode = 'return true'
    }
    
    try {
        upgrade.unlocked = new Function(unlockCode)
    } catch(e) {
        console.error('Error creating unlock function:', e)
        upgrade.unlocked = () => true
    }
    
    // Save layer configuration
    saveLayerConfig(layer)

    // Force update
    if (!window.isUpdating) {
        window.isUpdating = true
        updateTemp()
        updateTabFormats()
        window.isUpdating = false
    }

    closeFeatureEditor(); // Use the centralized close function
    openFeatureEditor(layer, 'upgrades'); // Re-open the feature list to show updated content
}

// Helper function to add a field to the feature editor
function addField(parent, label, value, onChange, isTextarea = false) {
    let container = document.createElement('div');
    container.style.marginBottom = '15px';
    
    let labelElem = document.createElement('div');
    labelElem.innerHTML = label;
    labelElem.style.marginBottom = '5px';
    labelElem.style.fontWeight = 'bold';
    container.appendChild(labelElem);
    
    let input;
    if (isTextarea) {
        input = document.createElement('textarea');
        input.style.height = '80px';
    } else {
        input = document.createElement('input');
        input.type = 'text';
    }
    
    input.value = value;
    input.style.width = '100%';
    input.style.padding = '8px';
    input.style.boxSizing = 'border-box';
    input.style.backgroundColor = '#333';
    input.style.color = '#fff';
    input.style.border = '1px solid #555';
    input.style.borderRadius = '4px';
    
    input.onchange = () => onChange(input.value);
    container.appendChild(input);
    
    parent.appendChild(container);
}

function createInputField(parent, label, name, value, onChange) {
    let container = document.createElement('div');
    container.className = 'input-container';
    
    let labelElem = document.createElement('label');
    labelElem.innerHTML = label;
    labelElem.htmlFor = name;
    container.appendChild(labelElem);
    
    let input = document.createElement('input');
    input.type = 'text';
    input.id = name;
    input.name = name;
    input.value = value;
    input.onchange = () => onChange(input.value);
    container.appendChild(input);
    
    parent.appendChild(container);
}

function createTextareaField(parent, label, name, value, onChange) {
    let container = document.createElement('div');
    container.className = 'input-container';
    
    let labelElem = document.createElement('label');
    labelElem.innerHTML = label;
    labelElem.htmlFor = name;
    container.appendChild(labelElem);
    
    let textarea = document.createElement('textarea');
    textarea.id = name;
    textarea.name = name;
    textarea.value = value;
    textarea.onchange = () => onChange(textarea.value);
    container.appendChild(textarea);
    
    parent.appendChild(container);
} 