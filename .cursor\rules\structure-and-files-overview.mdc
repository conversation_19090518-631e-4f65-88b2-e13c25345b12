---
alwaysApply: true
---
# Project Structure and Files Overview

This document provides a high-level overview of the project's file structure and key components. It serves as a map for understanding the codebase. Keep this file updated with significant changes.

## Root Directory
- **index.html**: Main entry point; loads all scripts and styles.
- **changelog.md**: Tracks project changes.
- **LICENSE**: Project license information.
- **README.md**: Project documentation and setup instructions.
- **resources/**: Contains images, fonts, and other assets (e.g., mod.png, Orbitron-Medium.ttf).
- **css/**: Stylesheets for UI components (e.g., general-style.css, tree-node.css).
- **docs/**: Documentation for TMT features and tutorials (e.g., upgrades.md, tutorials/getting-started.md).
- **js/**: Core JavaScript files organized into subdirectories.

## js/ Directory
### Main Files
- **game.js**: Core game loop and logic.
- **layers.js**: Defines base layers like 'p' and 'mod'.
- **mod.js**: Mod-related configurations.
- **save.js**: Save/load functionality.
- **tree.js**: Tree visualization logic.
- **utils.js**: General utility functions.

### features/
- **challengeEditor.js**: UI and logic for editing challenges.
- **editorUtils.js**: Helper functions for form elements in editors.
- **featureHtmlGenerators.js**: Generates HTML for feature displays.
- **featureManagement.js**: Manages adding/editing features like upgrades.
- **milestoneEditor.js**: UI and logic for editing milestones.
- **upgradeEditor.js**: UI and logic for editing upgrades.

### layers/
- **coreLayerDefinitions.js**: Defines core layer templates.
- **layerCreation.js**: Logic for creating new layers.
- **layerDefaults.js**: Applies default values to layers.
- **layerHtmlGenerators.js**: Generates layer-related HTML.
- **layerPropertyEditor.js**: Edits layer properties.
- **layerStyleUtils.js**: Handles layer node styles.

### mod/
- **modPanelUpdates.js**: Updates to the mod panel UI.

### saveLoad/
- **configManagement.js**: Manages mod configuration saving/loading.

### technical/
- **break_eternity.js**: Decimal library for large numbers.
- **canvas.js**: Canvas rendering for tree.
- **displays.js**: Display-related functions.
- **layerSupport.js**: Layer setup and support functions.
- **loader.js**: Game loading logic.
- **particleSystem.js**: Particle effects.
- **systemComponents.js**: Vue components for system UI.
- **temp.js**: Temporary data handling.

### ui/
- **customPrompt.js**: Provides a custom in-game modal for user input, replacing default browser prompts.
- **globalStyles.js**: Global UI styles.
- **tooltipHelpers.js**: Tooltip functionality.

### utils/
- **decimalHelpers.js**: Helpers for Decimal operations.
- **easyAccess.js**: Quick access to player data.
- **modEditor.js**: Mod editing tools.
- **NumberFormating.js**: Number formatting utilities.
- **options.js**: Game options.
- **save.js**: Save utilities.
- **themes.js**: Theme management.