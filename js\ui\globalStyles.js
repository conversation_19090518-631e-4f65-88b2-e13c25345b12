function injectGlobalStyles() {
document.head.insertAdjacentHTML('beforeend', `
<style>
.tooltip-trigger:hover .tooltip {
    display: block !important;
}
</style>
`)

// Update CSS for feature editor overlay
document.head.insertAdjacentHTML('beforeend', `
<style>
.feature-editor-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    background: rgba(0, 0, 0, 0.7) !important; /* Changed from 0.98 to 0.7 to make background more transparent */
    z-index: 100000 !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
}

.feature-editor-overlay > div {
    background: #1a1a1a !important;
    border: 1px solid rgba(255, 255, 255, 0.15) !important;
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.7) !important;
    position: relative !important;
    z-index: 100001 !important;
    width: 90% !important;
    max-width: 1200px !important;
    max-height: 90vh !important;
    overflow-y: auto !important;
    padding: 30px !important;
    border-radius: 15px !important;
}

.feature-editor-dialog {
    z-index: 100002 !important;
    background: #1a1a1a !important;
    border: 1px solid rgba(255, 255, 255, 0.15) !important;
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.7) !important;
    padding: 30px !important;
    border-radius: 15px !important;
    width: 90% !important;
    max-width: 800px !important;
    overflow-y: auto !important; /* Added to ensure content is scrollable */
    max-height: 85vh !important; /* Added to ensure dialog doesn't exceed viewport */
}

.feature-card {
    position: relative !important;
    z-index: 1 !important;
    background: rgba(30, 30, 30, 0.8) !important; /* Changed to be more visible */
    border-radius: 10px !important; /* Added border radius */
    padding: 15px !important; /* Added padding */
    margin-bottom: 15px !important; /* Added margin */
    border: 1px solid rgba(255, 255, 255, 0.1) !important; /* Added border */
}

.feature-card button {
    position: relative !important;
    z-index: 2 !important;
}

.layer-features-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)) !important;
    gap: 20px !important;
    margin-top: 15px !important;
}

.feature-editor-content {
    max-height: 300px !important;
    overflow-y: auto !important;
    padding: 10px !important;
    background: rgba(0, 0, 0, 0.2) !important;
    border-radius: 8px !important;
    margin-top: 10px !important;
}

.feature-editor-header {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    padding: 10px !important;
    background: rgba(0, 0, 0, 0.2) !important;
    border-radius: 8px !important;
}

.feature-list {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)) !important;
    gap: 20px !important;
    margin-top: 20px !important;
    max-height: 70vh !important;
    overflow-y: auto !important;
    padding: 10px !important;
}
</style>
`)
}

injectGlobalStyles() 