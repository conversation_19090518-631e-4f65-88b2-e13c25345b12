// Add helper function for instant saving and applying of basic info
function updateBasicInfo(key, value) {
    if (!player.mod) player.mod = {}
    
    // Update player.mod with the new value
    player.mod[key] = value
    
    switch(key) {
        case 'gameTitle':
            modInfo.name = value
            document.title = `${value} ${VERSION.num}`
            break
        case 'pointsName':
            modInfo.pointsName = value
            break
        case 'startPoints':
            let newPoints = new Decimal(value || 10)
            modInfo.initialStartPoints = newPoints
            player.mod.startPoints = newPoints.toString()
            // If current points match old starting points, update them
            if (player.points.eq(getStartPoints())) {
                player.points = newPoints
            }
            break
        case 'version':
            VERSION.num = value
            player.version = value // Update player.version too
            player.mod.version = value
            document.title = `${player.mod.gameTitle || modInfo.name} ${value}`
            // Update VERSION.withoutName and VERSION.withName
            VERSION.withoutName = "v" + VERSION.num + (VERSION.pre ? " Pre-Release " + VERSION.pre : VERSION.pre ? " Beta " + VERSION.beta : "")
            VERSION.withName = VERSION.withoutName + (VERSION.name ? ": " + VERSION.name : "")
            needCanvasUpdate = true
            break
        case 'versionName':
            VERSION.name = value
            player.mod.versionName = value
            // Update VERSION.withoutName and VERSION.withName
            VERSION.withoutName = "v" + VERSION.num + (VERSION.pre ? " Pre-Release " + VERSION.pre : VERSION.pre ? " Beta " + VERSION.beta : "")
            VERSION.withName = VERSION.withoutName + (VERSION.name ? ": " + VERSION.name : "")
            needCanvasUpdate = true
            break
        case 'discordName':
            modInfo.discordName = value
            break
        case 'discordLink':
            modInfo.discordLink = value
            break
        case 'author':
            modInfo.author = value
            break
        case 'changelog':
            modInfo.changelog = value
            break
        case 'winText':
            modInfo.winText = value
            break
        case 'offlineLimit':
            modInfo.offlineLimit = parseInt(value) || 1
            break
    }
    
    // Save all mod data
    let modData = {
        gameTitle: player.mod.gameTitle,
        pointsName: player.mod.pointsName,
        startPoints: player.mod.startPoints,
        discordLink: player.mod.discordLink,
        discordName: player.mod.discordName,
        author: player.mod.author,
        version: player.mod.version,
        versionName: player.mod.versionName,
        changelog: player.mod.changelog,
        winText: player.mod.winText,
        offlineLimit: player.mod.offlineLimit,
        layers: player.mod.layers,
        nextLayerId: player.mod.nextLayerId,
        selectedLayer: player.mod.selectedLayer
    }
    
    // Save to localStorage
    localStorage.setItem("modConfig", JSON.stringify(modData))
    
    // Force update game display - but prevent infinite recursion
    if (!window.isUpdating) {
        window.isUpdating = true
        updateTemp()
        updateTabFormats()
        updateVersionInfo()
        needCanvasUpdate = true
        setTimeout(() => {
            window.isUpdating = false
        }, 100)
    }
} 