// Helper function to update the feature card HTML for better visibility and layout
function getFeatureCardHTML(layerId, featureType) {
    const icons = {
        upgrades: '<img src="resources/icons/upgrades.svg" class="feature-icon">',
        milestones: '<img src="resources/icons/milestones.svg" class="feature-icon">',
        challenges: '<img src="resources/icons/challenges.svg" class="feature-icon">',
        edit: '<img src="resources/icons/edit.svg" class="feature-icon">',
        delete: '<img src="resources/icons/delete.svg" class="feature-icon">'
    };
    
    return `
    <div class='feature-card' style='
        background: rgba(20, 20, 20, 0.95);
        padding: 20px;
        border-radius: 12px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.5);
        border: 1px solid rgba(255, 255, 255, 0.15);
        position: relative;
        z-index: 1;
        margin-bottom: 15px;
        width: calc(100% - 40px);
        max-width: 400px;
    '>
        <div style='
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            gap: 20px;
        '>
            <h5 style='margin: 0; display: flex; align-items: center; gap: 10px; font-size: 16px; color: white;'>
                ${icons[featureType]}
                <span>${featureType.charAt(0).toUpperCase() + featureType.slice(1)}:</span>
            </h5>
            <button onclick='openFeatureEditor("${layerId}", "${featureType}")' style='
                background: rgba(75, 220, 19, 0.2);
                border: 1px solid rgba(75, 220, 19, 0.4);
                padding: 8px 15px;
                border-radius: 5px;
                color: white;
                cursor: pointer;
                font-size: 14px;
                display: flex;
                align-items: center;
                gap: 8px;
                transition: all 0.3s;
                z-index: 2;
            '>
                ${icons['edit']} Edit ${featureType}
            </button>
        </div>
    </div>`
}

function getUpgradesHTML(layerId, layer) {
    let html = '';
    if (!layer.upgrades || Object.keys(layer.upgrades).length === 0) {
        html = '<div class="no-features-message">No upgrades yet. Click "Add New Upgrade" to create one.</div>';
    } else {
        html = '<div class="feature-list-container grid grid-2col">'; // Use grid for better layout
        for (let id in layer.upgrades) {
            if (isNaN(id) || ['rows', 'cols'].includes(id)) continue;

            let upgrade = layer.upgrades[id];
            html += `
                <div class="feature-list-item">
                    <div class="feature-item-header">
                        <h4>${upgrade.title || `Upgrade ${id}`}</h4>
                        <div class="feature-item-actions">
                            <button class="edit-button" onclick='editFeature("${layerId}", "upgrades", "${id}")'>
                                <img src="resources/icons/edit.svg" class="feature-icon"> Edit
                            </button>
                            <button class="delete-button" onclick='deleteFeature("${layerId}", "upgrades", "${id}")'>
                                <img src="resources/icons/delete.svg" class="feature-icon"> Delete
                            </button>
                        </div>
                    </div>
                    <div class="feature-item-details">
                        <p>Description: ${upgrade.description || 'No description provided.'}</p>
                        <p>Cost: ${format(upgrade.cost)}</p>
                    </div>
                </div>
            `;
        }
        html += `</div>`;
    }
    return html;
}

function getMilestonesHTML(layerId, layer) {
    let html = ''
    if (!layer.milestones || Object.keys(layer.milestones).length === 0) {
        html = '<div class="no-features-message">No milestones yet. Click "Add New Milestone" to create one.</div>'
    } else {
        html = '<div class="feature-list-container grid grid-2col">'
        for (let id in layer.milestones) {
            let milestone = layer.milestones[id]
            html += `
                <div class="feature-list-item">
                    <div class="feature-item-header">
                        <h4>Milestone ${id}</h4>
                        <div class="feature-item-actions">
                            <button class="edit-button" onclick='editFeature("${layerId}", "milestones", "${id}")'>
                                <img src="resources/icons/edit.svg" class="feature-icon"> Edit
                            </button>
                            <button class="delete-button" onclick='deleteFeature("${layerId}", "milestones", "${id}")'>
                                <img src="resources/icons/delete.svg" class="feature-icon"> Delete
                            </button>
                        </div>
                    </div>
                    <div class="feature-item-details">
                        <p>Requirement: ${milestone.requirementDescription || 'No requirement'}</p>
                        <p>Effect: ${milestone.effectDescription || 'No effect'}</p>
                    </div>
                </div>
            `
        }
        html += `</div>`
    }
    return html
}

function getChallengesHTML(layerId, layer) {
    let html = ''
    if (!layer.challenges || Object.keys(layer.challenges).length === 0) {
        html = '<div class="no-features-message">No challenges yet. Click "Add New Challenge" to create one.</div>'
    } else {
        html = '<div class="feature-list-container grid grid-2col">'
        for (let id in layer.challenges) {
            let challenge = layer.challenges[id]
            html += `
                <div class="feature-list-item">
                    <div class="feature-item-header">
                        <h4>Challenge ${id}</h4>
                        <div class="feature-item-actions">
                            <button class="edit-button" onclick='editFeature("${layerId}", "challenges", "${id}")'>
                                <img src="resources/icons/edit.svg" class="feature-icon"> Edit
                            </button>
                            <button class="delete-button" onclick='deleteFeature("${layerId}", "challenges", "${id}")'>
                                <img src="resources/icons/delete.svg" class="feature-icon"> Delete
                            </button>
                        </div>
                    </div>
                    <div class="feature-item-details">
                        <p>Goal: ${challenge.goalDescription || format(challenge.goal)}</p>
                        <p>Reward: ${challenge.rewardDescription || 'No reward'}</p>
                    </div>
                </div>
            `
        }
        html += `</div>`
    }
    return html
}

function editUpgrade(layerId, upgradeId) {
    // ... existing code ...
}

function closeUpgradeEditor() {
    // ... existing code ...
}

function saveUpgradeEdit() {
    // ... existing code ...
}

function addField(container, labelText, inputType, initialValue = '', placeholder = '', fieldId, options = []) {
    // ... existing code ...
}

function createInputField(labelText, type, value, placeholder, id) {
    // ... existing code ...
}

function createTextareaField(labelText, value, placeholder, id) {
    // ... existing code ...
}