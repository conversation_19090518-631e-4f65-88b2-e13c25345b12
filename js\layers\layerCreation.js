window.createNewLayer = function() {
    let newId = `l${player.mod.nextLayerId}`;
    
    // Create temporary layer data first
    let layerData = {
        name: `Layer ${player.mod.nextLayerId}`,
        symbol: newId.toUpperCase(),
        position: 0,
        startData() { return {
            unlocked: true,
            points: new Decimal(0),
        }},
        color: "#4BDC13",
        requires: new Decimal(10),
        resource: "prestige points",
        baseResource: "points",
        baseAmount() {return new Decimal(player.points)},
        type: "normal",
        exponent: new Decimal(0.5),
        gainMult() { return new Decimal(1) },
        gainExp() { return new Decimal(1) },
        row: player.mod.nextLayerId,
        layerShown(){return true},
        resetDescription: "Reset for ",
        canReset() { return true },
        upgrades: {},
        milestones: {},
        challenges: {},
        tabFormat: {
            "Main": {
                content: [
                    ["display-text", "This is a new layer"],
                    ["blank", "20px"],
                    ["prestige-button", "", function() { return "" }],
                    ["blank", "20px"],
                ],
                unlocked: true
            }
        }
    };

    // Initialize temporary data
    tmp[newId] = {};
    for (let prop in layerData) {
        if (typeof layerData[prop] === 'function') {
            try {
                let result = layerData[prop]();
                // Ensure numeric results are Decimal
                if (typeof result === 'number') {
                    tmp[newId][prop] = new Decimal(result);
                } else {
                    tmp[newId][prop] = result;
                }
            } catch (e) {
                tmp[newId][prop] = layerData[prop];
            }
        } else if (typeof layerData[prop] === 'number') {
            tmp[newId][prop] = new Decimal(layerData[prop]);
        } else {
            tmp[newId][prop] = layerData[prop];
        }
    }

    // Ensure critical values are Decimal
    tmp[newId].gainMult = new Decimal(1);
    tmp[newId].gainExp = new Decimal(1);
    tmp[newId].points = new Decimal(0);
    tmp[newId].requires = new Decimal(10);
    tmp[newId].exponent = new Decimal(0.5);
    tmp[newId].base = new Decimal(1);
    tmp[newId].directMult = new Decimal(1);
    tmp[newId].softcap = new Decimal(Infinity);
    tmp[newId].softcapPower = new Decimal(1);

    // Add the layer
    addLayer(newId, layerData);
    
    // Apply defaults
    window.applyLayerDefaults(newId);

    // Initialize player data
    player[newId] = layerData.startData();
    player.mod.layers.push(newId);
    player.mod.selectedLayer = newId;
    player.mod.nextLayerId++;

    // Update tree layout
    layoutInfo.treeLayout = player.mod.layers.map(l => [l]);
    needCanvasUpdate = true;

    // Force updates - but prevent infinite recursion
    if (!window.isUpdating) {
        window.isUpdating = true;
        updateTemp();
        updateTabFormats();
        window.isUpdating = false;
    }
}; 