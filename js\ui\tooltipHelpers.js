// Add helper functions at the top of the file
function getLayerTooltip(layer, type) {
    const tooltips = {
        type: "Layer type determines how prestige gain is calculated:\n- normal: gain depends on current resources\n- static: gain depends on total resources",
        resource: "The currency gained when prestiging in this layer",
        requires: "The amount of base resource needed to prestige",
        baseResource: "The resource that this layer's prestige is based on",
        exponent: "Affects how quickly prestige gain increases"
    }
    return tooltips[type] || ""
} 