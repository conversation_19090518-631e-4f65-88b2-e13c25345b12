function addLayerDefinitions() {
    addLayer("p", {
        name: "prestige", // This is optional, only used in a few places, If absent it just uses the layer id.
        symbol: "P", // This appears on the layer's node. Default is the id with the first letter capitalized
        position: 0, // Horizontal position within a row. By default it uses the layer id and sorts in alphabetical order
        startData() { return {
            unlocked: true,
            points: new Decimal(0),
            upgrades: {},
            milestones: {},
            challenges: {},
        }},
        color: "#4BDC13",
        requires: new Decimal(10), // Can be a function that takes requirement increases into account
        resource: "prestige points", // Name of prestige currency
        baseResource: "points", // Name of resource prestige is based on
        baseAmount() {return player.points}, // Get the current amount of baseResource
        type: "normal", // normal: cost to gain currency depends on amount gained. static: cost depends on how much you already have
        exponent: 0.5, // Prestige currency exponent
        gainMult() { // Calculate the multiplier for main currency from bonuses
            mult = new Decimal(1)
            return mult
        },
        gainExp() { // Calculate the exponent on main currency from bonuses
            return new Decimal(1)
        },
        row: 0, // Row the layer is in on the tree (0 is the first row)
        hotkeys: [
            {key: "p", description: "P: Reset for prestige points", onPress(){if (canReset(this.layer)) doReset(this.layer)}},
        ],
        layerShown(){return true}
    })

    addLayer("mod", {
        name: "mod",
        symbol: "M",
        position: 0,
        startData() { 
            // Try to load saved mod config from localStorage
            let savedConfig = localStorage.getItem("modConfig")
            if (savedConfig) {
                let config = JSON.parse(savedConfig)
                return {
                    unlocked: true,
                    points: new Decimal(0),
                    gameTitle: config.gameTitle || modInfo.name,
                    pointsName: config.pointsName || modInfo.pointsName,
                    startPoints: config.startPoints || modInfo.initialStartPoints,
                    discordLink: config.discordLink || modInfo.discordLink,
                    discordName: config.discordName || modInfo.discordName,
                    author: config.author || modInfo.author,
                    version: config.version || VERSION.num,
                    versionName: config.versionName || VERSION.name,
                    changelog: config.changelog || "No changes logged yet.",
                    winText: config.winText || "Congratulations! You beat the game!",
                    offlineLimit: config.offlineLimit || 1,
                    exportString: "",
                    layers: config.layers || ["p"],
                    nextLayerId: config.nextLayerId || 1,
                    selectedLayer: String(config.selectedLayer) || "p",
                    upgrades: {},
                    milestones: {},
                    challenges: {},
                }
            }
            return {
                unlocked: true,
                points: new Decimal(0),
                gameTitle: modInfo.name,
                pointsName: modInfo.pointsName,
                startPoints: modInfo.initialStartPoints,
                discordLink: modInfo.discordLink,
                discordName: modInfo.discordName,
                author: modInfo.author,
                version: VERSION.num,
                versionName: VERSION.name,
                changelog: "No changes logged yet.",
                winText: "Congratulations! You beat the game!",
                offlineLimit: 1,
                exportString: "",
                layers: ["p"],
                nextLayerId: 1,
                selectedLayer: "p",
                upgrades: {},
                milestones: {},
                challenges: {},
            }
        },
        tooltip: "Mod",
        image: "resources/mod.png",
        nodeStyle: {
            "background-size": "contain",
            "background-position": "center",
            "background-repeat": "no-repeat",
            "width": "60px",
            "height": "60px",
            "border-radius": "50%",
        },
        tabFormat: {
            "Basic Information": {
                content: [
                    ["display-text", "<h2 style='text-align: center; margin-bottom: 20px;'>Basic Information</h2>"],
                    ["row", [
                        ["display-text", "<div style='width: 200px; text-align: right; margin-right: 10px;'><h3>Tree Name:</h3></div>"],
                        ["textinput", ["gameTitle"]]
                    ]],
                    ["blank", "20px"],
                    ["row", [
                        ["display-text", "<div style='width: 200px; text-align: right; margin-right: 10px;'><h3>Points Name:</h3></div>"],
                        ["textinput", ["pointsName"]]
                    ]],
                    ["blank", "20px"],
                    ["row", [
                        ["display-text", "<div style='width: 200px; text-align: right; margin-right: 10px;'><h3>Starting Points:</h3></div>"],
                        ["textinput", ["startPoints"]]
                    ]],
                    ["blank", "20px"],
                    ["row", [
                        ["display-text", "<div style='width: 200px; text-align: right; margin-right: 10px;'><h3>Author Name:</h3></div>"],
                        ["textinput", ["author"]]
                    ]],
                    ["blank", "20px"],
                    ["row", [
                        ["display-text", "<div style='width: 200px; text-align: right; margin-right: 10px;'><h3>Version:</h3></div>"],
                        ["textinput", ["version"]]
                    ]],
                    ["blank", "20px"],
                    ["row", [
                        ["display-text", "<div style='width: 200px; text-align: right; margin-right: 10px;'><h3>Version Name:</h3></div>"],
                        ["textinput", ["versionName"]]
                    ]],
                    ["blank", "20px"],
                    ["row", [
                        ["display-text", "<div style='width: 200px; text-align: right; margin-right: 10px;'><h3>Changelog:</h3></div>"],
                        ["textinput", ["changelog"]]
                    ]],
                    ["blank", "20px"],
                    ["row", [
                        ["display-text", "<div style='width: 200px; text-align: right; margin-right: 10px;'><h3>Win Text:</h3></div>"],
                        ["textinput", ["winText"]]
                    ]],
                    ["blank", "20px"],
                    ["row", [
                        ["display-text", "<div style='width: 200px; text-align: right; margin-right: 10px;'><h3>Offline Time Limit (minutes):</h3></div>"],
                        ["textinput", ["offlineLimit"]]
                    ]],
                    ["blank", "20px"],
                    ["row", [
                        ["display-text", "<div style='width: 200px; text-align: right; margin-right: 10px;'><h3>Discord Server Name:</h3></div>"],
                        ["textinput", ["discordName"]]
                    ]],
                    ["blank", "20px"],
                    ["row", [
                        ["display-text", "<div style='width: 200px; text-align: right; margin-right: 10px;'><h3>Discord Server Link:</h3></div>"],
                        ["textinput", ["discordLink"]]
                    ]],
                    ["blank", "40px"],
                    ["display-text", "<h2 style='text-align: center;'>Export Mod</h2>"],
                    ["display-text", function() { return "<div style='padding: 10px; word-wrap: break-word; margin: 10px 0;'>" + player.mod.exportString + "</div>" }],
                    ["row", [["clickable", "export"]]]
                ],
                style: {
                    "background-color": "transparent",
                    "border": "none",
                    "box-shadow": "none",
                    "width": "100%",
                    "margin": "0 auto",
                    "max-width": "800px",
                    "font-family": "Orbitron"
                }
            },
            "Layers": {
                content: [
                    ["display-text", "<h2 class='mod-text' style='margin-bottom: 20px'>Game Layers</h2>"],
                    ["row", [["clickable", "newLayer"]]],
                    ["display-text", function() { 
                        if (player.mod.selectedLayer) return ""
                        let text = "<div style='text-align: left; margin: 10px 0;' class='mod-text'>"
                        text += "<div style='margin-bottom: 20px;'>Click on a layer to edit its properties:</div>"
                        for(let i = 0; i < player.mod.layers.length; i++) {
                            let layerId = player.mod.layers[i]
                            text += `
                                <div class='layer-card' style='
                                    background: rgba(255, 255, 255, 0.1);
                                    padding: 15px;
                                    border-radius: 10px;
                                    display: flex;
                                    align-items: center;
                                    justify-content: space-between;
                                    margin: 10px 0;
                                    cursor: pointer;
                                    transition: all 0.3s;
                                ' onclick='player.mod.selectedLayer=String("${layerId}")'>
                                    <div>Layer ${i+1}: ${layers[layerId].name || layerId}</div>
                                    <div style='
                                        width: 40px;
                                        height: 40px;
                                        background: ${layers[layerId].color};
                                        border-radius: 50%;
                                        display: flex;
                                        align-items: center;
                                        justify-content: center;
                                        font-weight: bold;
                                    '>${layers[layerId].symbol}</div>
                                </div>`
                        }
                        text += "</div>"
                        return text
                    }],
                    ["display-text", function() {
                        if (!player.mod.selectedLayer) return ""
                        let layer = layers[player.mod.selectedLayer]
                        return `
                        <div class='mod-text' style='margin: 20px 0;'>
                            <div style='
                                display: flex;
                                align-items: center;
                                margin-bottom: 20px;
                                gap: 20px;
                            '>
                                <button onclick='player.mod.selectedLayer=""' style='
                                    background: rgba(255, 255, 255, 0.1);
                                    border: none;
                                    padding: 10px 15px;
                                    border-radius: 8px;
                                    color: white;
                                    cursor: pointer;
                                    transition: all 0.3s;
                                    display: flex;
                                    align-items: center;
                                    gap: 5px;
                                '>
                                    <i class="fas fa-arrow-left"></i> Back to Layers
                                </button>
                                <div style='
                                    display: flex;
                                    align-items: center;
                                    gap: 10px;
                                '>
                                    <span style='font-size: 24px;'>Editing:</span>
                                    <div style='
                                        background: rgba(255, 255, 255, 0.1);
                                        padding: 10px 20px;
                                        border-radius: 10px;
                                        display: flex;
                                        align-items: center;
                                        gap: 15px;
                                    '>
                                        <span>${layer.name || layer.id}</span>
                                        <div style='
                                            width: 40px;
                                            height: 40px;
                                            background: ${layer.color};
                                            border-radius: 50%;
                                            display: flex;
                                            align-items: center;
                                            justify-content: center;
                                            font-weight: bold;
                                        '>${layer.symbol}</div>
                                    </div>
                                </div>
                            </div>

                            <div style='
                                background: rgba(255, 255, 255, 0.05);
                                border-radius: 15px;
                                padding: 20px;
                                margin-bottom: 20px;
                            '>
                                <h4 style='margin-bottom: 15px;'>Basic Properties</h4>
                                <div style='
                                    display: grid;
                                    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                                    gap: 10px;
                                '>
                                    <button onclick='editLayerProperty("name")' style='
                                        background: rgba(255, 255, 255, 0.1);
                                        border: none;
                                        padding: 10px;
                                        border-radius: 8px;
                                        color: white;
                                        cursor: pointer;
                                        transition: all 0.3s;
                                    '>
                                        <i class="fas fa-edit"></i> Name: ${layer.name || layer.id}
                                    </button>
                                    <button onclick='editLayerProperty("color")' style='
                                        background: rgba(255, 255, 255, 0.1);
                                        border: none;
                                        padding: 10px;
                                        border-radius: 8px;
                                        color: white;
                                        cursor: pointer;
                                        transition: all 0.3s;
                                    '>
                                        <i class="fas fa-palette"></i> Color: <span style='color: ${layer.color}'>${layer.color}</span>
                                    </button>
                                    <button onclick='editLayerProperty("symbol")' style='
                                        background: rgba(255, 255, 255, 0.1);
                                        border: none;
                                        padding: 10px;
                                        border-radius: 8px;
                                        color: white;
                                        cursor: pointer;
                                        transition: all 0.3s;
                                    '>
                                        <i class="fas fa-font"></i> Symbol: ${layer.symbol}
                                    </button>
                                </div>
                            </div>

                            <div style='
                                background: rgba(255, 255, 255, 0.05);
                                border-radius: 15px;
                                padding: 20px;
                                margin-bottom: 20px;
                            '>
                                <h4 style='margin-bottom: 15px;'>Layer Mechanics</h4>
                                <div style='
                                    display: grid;
                                    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                                    gap: 10px;
                                '>
                                    <div class='mechanic-button' style='position: relative;'>
                                        <button onclick='editLayerMechanic("type")' style='
                                            background: rgba(255, 255, 255, 0.1);
                                            border: none;
                                            padding: 10px;
                                            border-radius: 8px;
                                            color: white;
                                            cursor: pointer;
                                            transition: all 0.3s;
                                            width: 100%;
                                            text-align: left;
                                            display: flex;
                                            align-items: center;
                                            justify-content: space-between;
                                        '>
                                            <span><i class="fas fa-cog"></i> Type: ${layer.type || "normal"}</span>
                                            <i class="fas fa-question-circle info-icon"></i>
                                        </button>
                                        <div class='tooltip-text'>${getLayerTooltip("type")}</div>
                                    </div>
                                    <div class='mechanic-button' style='position: relative;'>
                                        <button onclick='editLayerMechanic("resource")' style='
                                            background: rgba(255, 255, 255, 0.1);
                                            border: none;
                                            padding: 10px;
                                            border-radius: 8px;
                                            color: white;
                                            cursor: pointer;
                                            transition: all 0.3s;
                                            width: 100%;
                                            text-align: left;
                                            display: flex;
                                            align-items: center;
                                            justify-content: space-between;
                                        '>
                                            <span><i class="fas fa-coins"></i> Resource: ${layer.resource}</span>
                                            <i class="fas fa-question-circle info-icon"></i>
                                        </button>
                                        <div class='tooltip-text'>${getLayerTooltip("resource")}</div>
                                    </div>
                                    <div class='mechanic-button' style='position: relative;'>
                                        <button onclick='editLayerMechanic("requires")' style='
                                            background: rgba(255, 255, 255, 0.1);
                                            border: none;
                                            padding: 10px;
                                            border-radius: 8px;
                                            color: white;
                                            cursor: pointer;
                                            transition: all 0.3s;
                                            width: 100%;
                                            text-align: left;
                                            display: flex;
                                            align-items: center;
                                            justify-content: space-between;
                                        '>
                                            <span><i class="fas fa-unlock"></i> Required: ${layer.requires}</span>
                                            <i class="fas fa-question-circle info-icon"></i>
                                        </button>
                                        <div class='tooltip-text'>${getLayerTooltip("requires")}</div>
                                    </div>
                                    <div class='mechanic-button' style='position: relative;'>
                                        <button onclick='editLayerMechanic("resetDescription")' style='
                                            background: rgba(255, 255, 255, 0.1);
                                            border: none;
                                            padding: 10px;
                                            border-radius: 8px;
                                            color: white;
                                            cursor: pointer;
                                            transition: all 0.3s;
                                            width: 100%;
                                            text-align: left;
                                            display: flex;
                                            align-items: center;
                                            justify-content: space-between;
                                        '>
                                            <span><i class="fas fa-sync"></i> Prestige Text: ${layer.resetDescription || "Reset for "}</span>
                                            <i class="fas fa-question-circle info-icon"></i>
                                        </button>
                                        <div class='tooltip-text'>The text shown on the prestige button</div>
                                    </div>
                                </div>
                            </div>

                            <div style='
                                background: rgba(255, 255, 255, 0.05);
                                border-radius: 15px;
                                padding: 20px;
                            '>
                                <h4 style='margin-bottom: 15px;'>Layer Features</h4>
                                <div class='layer-features-grid'>
                                    ${(function() {
                                        return getFeatureCardHTML(String(layer.id), "upgrades") + getFeatureCardHTML(String(layer.id), "milestones") + getFeatureCardHTML(String(layer.id), "challenges");
                                    })()}
                                </div>
                            </div>
                        </div>
                        `
                    }]
                ],
                style: {
                    "border-radius": "10px",
                    "padding": "20px",
                    "margin": "10px"
                }
            }
        },
        clickables: {
            "export": {
                display: "Export Mod",
                onClick() {
                    let exportObj = {
                        name: player.mod.gameTitle,
                        pointsName: player.mod.pointsName,
                        initialStartPoints: new Decimal(player.mod.startPoints),
                        discordLink: player.mod.discordLink,
                        discordName: player.mod.discordName,
                        author: player.mod.author
                    };
                    player.mod.exportString = btoa(JSON.stringify(exportObj));
                },
                style: {
                    'min-height': '50px',
                    'width': '200px',
                    'background': 'rgba(255, 255, 255, 0.1)',
                    'border': 'none',
                    'border-radius': '8px',
                    'margin': '10px auto',
                    'color': 'white',
                    'cursor': 'pointer',
                    'font-size': '16px',
                    'padding': '10px 20px',
                    'display': 'block',
                    'transition': 'all 0.3s'
                }
            },
            "newLayer": {
                display: "Add New Layer",
                canClick() { return true },
                onClick() {
                    let newId = `l${player.mod.nextLayerId}`
                    
                    // Create temporary layer data first
                    let layerData = {
                        name: `Layer ${player.mod.nextLayerId}`,
                        symbol: newId.toUpperCase(),
                        position: 0,
                        startData() { return {
                            unlocked: true,
                            points: new Decimal(0),
                            upgrades: {},
                            milestones: {},
                            challenges: {},
                        }},
                        color: "#4BDC13",
                        requires: new Decimal(10),
                        resource: "prestige points",
                        baseResource: "points",
                        baseAmount() {return new Decimal(player.points)},
                        type: "normal",
                        exponent: new Decimal(0.5),
                        gainMult() { return new Decimal(1) },
                        gainExp() { return new Decimal(1) },
                        row: player.mod.nextLayerId,
                        layerShown(){return true},
                        resetDescription: "Reset for ",
                        canReset() { return true },
                        upgrades: {},
                        milestones: {},
                        challenges: {},
                    }

                    // Initialize temporary data
                    tmp[newId] = {}
                    for (let prop in layerData) {
                        if (typeof layerData[prop] === 'function') {
                            try {
                                let result = layerData[prop]()
                                // Ensure numeric results are Decimal
                                if (typeof result === 'number') {
                                    tmp[newId][prop] = new Decimal(result)
                                } else {
                                    tmp[newId][prop] = result
                                }
                            } catch (e) {
                                tmp[newId][prop] = layerData[prop]
                            }
                        } else if (typeof layerData[prop] === 'number') {
                            tmp[newId][prop] = new Decimal(layerData[prop])
                        } else {
                            tmp[newId][prop] = layerData[prop]
                        }
                    }

                    // Ensure critical values are Decimal
                    tmp[newId].gainMult = new Decimal(1)
                    tmp[newId].gainExp = new Decimal(1)
                    tmp[newId].points = new Decimal(0)
                    tmp[newId].requires = new Decimal(10)
                    tmp[newId].exponent = new Decimal(0.5)

                    // Add the layer
                    addLayer(newId, layerData)
                    
                    // Initialize player data
                    player[newId] = layerData.startData()
                    player.mod.layers.push(String(newId))
                    player.mod.selectedLayer = String(newId)
                    player.mod.nextLayerId++

                    // Update tree layout
                    layoutInfo.treeLayout = player.mod.layers.map(l => [l])
                    needCanvasUpdate = true

                    // Force updates - but prevent infinite recursion
                    if (!window.isUpdating) {
                        window.isUpdating = true
                        updateTemp()
                        updateTabFormats()
                        window.isUpdating = false
                    }
                },
                style: {
                    'min-height': '50px',
                    'width': '200px',
                    'background': 'rgba(255, 255, 255, 0.1)',
                    'border': 'none',
                    'border-radius': '8px',
                    'margin': '20px auto',
                    'color': 'white',
                    'cursor': 'pointer',
                    'font-family': 'Orbitron',
                    'font-size': '16px',
                    'padding': '10px 20px',
                    'transition': 'all 0.3s'
                }
            }
        },
        update(diff) {
            // Prevent circular updates
            if (window.isUpdating) return
            window.isUpdating = true
            
            modInfo.name = player.mod.gameTitle
            modInfo.pointsName = player.mod.pointsName
            modInfo.initialStartPoints = new Decimal(player.mod.startPoints)
            modInfo.discordLink = player.mod.discordLink
            modInfo.discordName = player.mod.discordName
            modInfo.author = player.mod.author

            // Save mod config to localStorage
            let config = {
                gameTitle: player.mod.gameTitle,
                pointsName: player.mod.pointsName,
                startPoints: player.mod.startPoints,
                discordLink: player.mod.discordLink,
                discordName: player.mod.discordName,
                author: player.mod.author,
                layers: player.mod.layers,
                nextLayerId: player.mod.nextLayerId,
                selectedLayer: player.mod.selectedLayer
            }
            localStorage.setItem("modConfig", JSON.stringify(config))

            // Load saved layer configurations and apply node styles
            let layerConfig = localStorage.getItem("layerConfig")
            if (layerConfig) {
                layerConfig = JSON.parse(layerConfig)
                for (let layerId in layerConfig) {
                    if (layers[layerId]) {
                        // Apply saved properties
                        Object.assign(layers[layerId], layerConfig[layerId])
                        if (tmp[layerId]) {
                            Object.assign(tmp[layerId], layerConfig[layerId])
                            // Apply node style
                            tmp[layerId].nodeStyle = getNodeStyle(layerId)
                        }
                    }
                }
            }
            
            // Ensure all layers have proper Decimal values
            for (let layerId in layers) {
                if (tmp[layerId]) {
                    tmp[layerId].gainExp = ensureDecimal(layers[layerId].gainExp)
                    tmp[layerId].gainMult = ensureDecimal(layers[layerId].gainMult)
                    tmp[layerId].baseAmount = ensureDecimal(layers[layerId].baseAmount)
                    tmp[layerId].requires = ensureDecimal(layers[layerId].requires)
                    tmp[layerId].exponent = ensureDecimal(layers[layerId].exponent)
                }
            }
            
            needCanvasUpdate = true
            window.isUpdating = false
        }
    })
} 