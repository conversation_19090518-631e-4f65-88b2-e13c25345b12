---
description: 
globs: 
alwaysApply: true
---

- **What's this project:** This project is a visual editor, called "The Super Modding Tree", that allows users to create mods for the **The Modding Tree (TMT)** framework `without writing any code`, and test in the same app since the game is build using TMT as base. The primary user is a mod creator, not a programmer.


- **Data Flow:** The user's mod configuration is stored in `player.mod` and persisted in `localStorage`. This data is then used to dynamically generate the TMT layer objects and their properties at runtime. Game sava data and mod personalization are `DIFFERENT`.

- **JavaScript Environment, Compatibility & Script Loading:** The game's is built to work by `index.html` without any server, and all script initialization is made in that html file.


- **Utilize and Enhance Helper Functions:** When implementing new functionality or fixing bugs, prioritize the use of existing `helper functions` across the codebase. If a suitable helper does not exist, create a new, reusable function within the most `adequate file`. If an existing helper can be improved to be more robust or cover more use cases, enhance it rather than creating a one-off solution.

**Codebase Awareness**: The `[structure-and-files-overview.mdc](.cursor/rules/structure-and-files-overview.mdc)` file provides a vital, high-level map of the project architecture and is your single source of truth for understanding the codebase structure. You MUST keep this file updated. If you perform any significant structural changes—such as creating, moving, or deleting `core` files/directories, or refactoring the primary data flow—you MUST update this overview file to reflect those changes within the same turn. This discipline ensures your context remains accurate throughout our development sessions and prevents confusion.